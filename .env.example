# Cấu hình môi trường cho Face Recognition API

# MongoDB Configuration (local installation)
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=face_recognition_db

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=true

# File Upload Configuration
MAX_FILE_SIZE_MB=10
ALLOWED_EXTENSIONS=.jpg,.jpeg,.png

# Face Recognition Configuration
FACE_DETECTION_MODEL=hog
DEFAULT_TOLERANCE=0.5
MAX_IMAGE_WIDTH=1920
MAX_IMAGE_HEIGHT=1080

# Directory Configuration
PUBLIC_DIR=public
IMAGES_DIR=public/images

# Security (cho production)
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration
ALLOWED_ORIGINS=*
# Trong production: ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# MongoDB Service Configuration (Linux)
MONGODB_SERVICE_NAME=mongod
MONGODB_LOG_PATH=/var/log/mongodb/mongod.log
MONGODB_DATA_PATH=/var/lib/mongodb