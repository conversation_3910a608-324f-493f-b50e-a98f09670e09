# 🚀 Quick Start - Face Recognition API (Không sử dụng Docker)

## C<PERSON>ch chạy nhanh nhất (5 phút)

### 1. <PERSON><PERSON><PERSON> bị
```bash
# Clone hoặc tải về project
cd face_compare

# Cấp quyền cho scripts
chmod +x setup.sh start.sh install_system_deps.sh
```

### 2. Cài đặt tự động
```bash
# Chạy script setup (sẽ tự động cài đặt mọi thứ bao gồm MongoDB)
bash setup.sh
```

### 3. Khởi động
```bash
# Khởi động API (sẽ tự động start MongoDB)
bash start.sh
```

### 4. Truy cập
- API: http://localhost:8000
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

---

## Cách thủ công (nếu script lỗi)

### 1. <PERSON><PERSON><PERSON> tra Python3
```bash
python3 --version  # Cần >= 3.8
pip3 --version
```

### 2. Cài dependencies hệ thống
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3-dev python3-pip python3-venv build-essential cmake libopenblas-dev liblapack-dev

# macOS
brew install cmake boost boost-python3 pkg-config
```

### 3. Cài đặt MongoDB

#### Ubuntu/Debian:
```bash
# Thêm MongoDB repository
curl -fsSL https://pgp.mongodb.com/server-7.0.asc | sudo gpg -o /usr/share/keyrings/mongodb-server-7.0.gpg --dearmor

echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-7.0.gpg ] https://repo.mongodb.org/apt/ubuntu $(lsb_release -cs)/mongodb-org/7.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-7.0.list

sudo apt-get update
sudo apt-get install -y mongodb-org

# Khởi động MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod
```

#### macOS:
```bash
# Cài đặt MongoDB với Homebrew
brew tap mongodb/brew
brew install mongodb-community

# Khởi động MongoDB
brew services start mongodb/brew/mongodb-community
```

### 4. Tạo môi trường ảo
```bash
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# venv\Scripts\activate    # Windows
```

### 5. Cài packages Python
```bash
pip install --upgrade pip
pip install numpy
pip install cmake dlib
pip install -r requirements.txt
```

### 6. Chạy API
```bash
python run.py
```

---

## Test nhanh API

### 1. Đăng ký người mới
```bash
curl -X POST "http://localhost:8000/api/persons/register" \
  -F "name=Test User" \
  -F "email=<EMAIL>" \
  -F "image=@path/to/photo.jpg"
```

### 2. Xác thực khuôn mặt
```bash
curl -X POST "http://localhost:8000/api/face-recognition/verify" \
  -F "image=@path/to/test.jpg" \
  -F "threshold=0.5"
```

### 3. Xem danh sách
```bash
curl "http://localhost:8000/api/persons/"
```

---

## Troubleshooting

### Lỗi cài đặt face_recognition
```bash
# Linux
sudo apt install build-essential cmake libboost-python-dev libdlib-dev

# macOS
xcode-select --install
brew install cmake boost dlib
```

### MongoDB không kết nối được
```bash
# Kiểm tra MongoDB đang chạy
# Linux
sudo systemctl status mongod

# macOS
brew services list | grep mongo

# Khởi động lại MongoDB
# Linux
sudo systemctl restart mongod

# macOS
brew services restart mongodb/brew/mongodb-community
```

### MongoDB logs
```bash
# Linux
sudo tail -f /var/log/mongodb/mongod.log

# macOS
tail -f /usr/local/var/log/mongodb/mongo.log
```

### Port 8000 bị chiếm
```bash
# Kiểm tra port
lsof -i :8000

# Thay đổi port trong run.py hoặc
uvicorn app.main:app --port 8001
```

### Permission denied khi cài MongoDB
```bash
# Linux - đảm bảo có quyền sudo
sudo chown -R mongodb:mongodb /var/lib/mongodb
sudo chown mongodb:mongodb /tmp/mongodb-27017.sock

# Restart service
sudo systemctl restart mongod
```

---

## Các lệnh hữu ích

```bash
# Kích hoạt môi trường ảo
source venv/bin/activate

# Kiểm tra MongoDB status
# Linux
sudo systemctl status mongod

# macOS
brew services list | grep mongo

# Test API với script
python test_api.py --register-image photo.jpg --verify-image test.jpg

# Xem thống kê
curl http://localhost:8000/api/persons/stats/summary

# Kiểm tra health
curl http://localhost:8000/health

# Kết nối MongoDB shell
mongosh

# Xem databases
mongosh --eval "show dbs"

# Xem collections trong database
mongosh face_recognition_db --eval "show collections"
```

---

## MongoDB Commands

```bash
# Khởi động MongoDB
# Linux
sudo systemctl start mongod

# macOS
brew services start mongodb/brew/mongodb-community

# Dừng MongoDB
# Linux
sudo systemctl stop mongod

# macOS
brew services stop mongodb/brew/mongodb-community

# Restart MongoDB
# Linux
sudo systemctl restart mongod

# macOS
brew services restart mongodb/brew/mongodb-community

# Xem MongoDB logs
# Linux
sudo journalctl -u mongod

# macOS
brew services list mongodb/brew/mongodb-community
```

---

## Cấu trúc API

- `POST /api/persons/register` - Đăng ký người mới
- `GET /api/persons/` - Danh sách người
- `POST /api/face-recognition/verify` - Xác thực khuôn mặt
- `GET /docs` - Swagger UI
- `GET /health` - Health check

## MongoDB Database

- Database: `face_recognition_db`
- Collection: `persons`
- Connection: `mongodb://localhost:27017`

🎉 **Chúc mừng! API đã sẵn sàng sử dụng mà không cần Docker!**