# Face Recognition API

API xác thực khuôn mặt được xây dựng bằng FastAPI, MongoDB và face_recognition library.

## Tính năng

- 🔍 **Nhận diện khuôn mặt**: Phát hiện và nhận diện nhiều khuôn mặt trong một ảnh
- 👤 **Quản lý người dùng**: <PERSON><PERSON><PERSON> ký, cậ<PERSON> nh<PERSON>, xóa thông tin người dùng
- 📊 **API RESTful**: Swagger UI tự động, validation dữ liệu
- 💾 **MongoDB**: Lưu trữ thông tin và mã hóa khuôn mặt
- 📁 **Lưu trữ ảnh**: Tự động lưu ảnh vào thư mục public
- 🎯 **Độ chính xác cao**: Sử dụng face_recognition library (dlib)

## ML Framework được sử dụng

**face_recognition** - T<PERSON><PERSON> viện miễn phí và dễ sử dụng nhất:
- Dựa trên dlib với độ chính xác cao (99.38%)
- API đơn giản, tài liệu đầy đủ
- Hỗ trợ nhiều định dạng ảnh
- Không cần GPU, chạy được trên CPU
- Hoàn toàn miễn phí và open source

## Yêu cầu hệ thống

- Python 3.8+
- MongoDB 4.4+
- RAM: 2GB+ (khuyến nghị 4GB)
- Disk: 1GB+ cho dependencies

## Cài đặt

### 1. Cài đặt system dependencies

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3-dev python3-pip python3-venv build-essential cmake libopenblas-dev liblapack-dev pkg-config libdlib-dev

# macOS
brew install cmake boost boost-python3 pkg-config
```

### 2. Cài đặt MongoDB

#### Ubuntu/Debian:
```bash
# Thêm MongoDB repository
curl -fsSL https://pgp.mongodb.com/server-7.0.asc | sudo gpg -o /usr/share/keyrings/mongodb-server-7.0.gpg --dearmor

echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-7.0.gpg ] https://repo.mongodb.org/apt/ubuntu $(lsb_release -cs)/mongodb-org/7.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-7.0.list

sudo apt-get update
sudo apt-get install -y mongodb-org

# Khởi động MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod
```

#### macOS:
```bash
# Cài đặt MongoDB với Homebrew
brew tap mongodb/brew
brew install mongodb-community

# Khởi động MongoDB
brew services start mongodb/brew/mongodb-community
```

### 3. Cài đặt Python dependencies

```bash
# Tạo virtual environment
python3 -m venv venv
source venv/bin/activate

# Cài đặt packages
pip install --upgrade pip
pip install -r requirements.txt
```

### 4. Thiết lập biến môi trường (tùy chọn)

```bash
export MONGODB_URL="mongodb://localhost:27017"
export DATABASE_NAME="face_recognition_db"
```

### 5. Chạy ứng dụng

```bash
# Với script setup tự động
bash setup.sh

# Hoặc thủ công
source venv/bin/activate
python run.py

# Hoặc với uvicorn
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

## Khởi động MongoDB

### Ubuntu/Debian:
```bash
# Khởi động MongoDB
sudo systemctl start mongod

# Kiểm tra trạng thái
sudo systemctl status mongod

# Bật tự động khởi động
sudo systemctl enable mongod
```

### macOS:
```bash
# Khởi động MongoDB
brew services start mongodb/brew/mongodb-community

# Kiểm tra trạng thái
brew services list | grep mongo
```

## API Endpoints

### 🏠 Trang chủ
- `GET /` - Health check
- `GET /health` - Kiểm tra sức khỏe hệ thống
- `GET /docs` - Swagger UI
- `GET /redoc` - ReDoc documentation

### 👤 Quản lý người dùng
- `POST /api/persons/register` - Đăng ký người mới với ảnh
- `GET /api/persons/` - Lấy danh sách người (có phân trang)
- `GET /api/persons/{id}` - Lấy thông tin chi tiết
- `PUT /api/persons/{id}` - Cập nhật thông tin
- `DELETE /api/persons/{id}` - Xóa người
- `GET /api/persons/stats/summary` - Thống kê tổng quan

### 🔍 Nhận diện khuôn mặt
- `POST /api/face-recognition/verify` - Xác thực nhiều khuôn mặt
- `POST /api/face-recognition/verify-single` - Xác thực một khuôn mặt
- `GET /api/face-recognition/settings` - Xem cài đặt nhận diện

## Sử dụng API

### 1. Đăng ký người mới

```bash
curl -X POST "http://localhost:8000/api/persons/register" \
  -H "Content-Type: multipart/form-data" \
  -F "name=Nguyễn Văn A" \
  -F "email=<EMAIL>" \
  -F "phone=**********" \
  -F "description=Nhân viên IT" \
  -F "image=@path/to/photo.jpg"
```

### 2. Xác thực khuôn mặt

```bash
curl -X POST "http://localhost:8000/api/face-recognition/verify" \
  -H "Content-Type: multipart/form-data" \
  -F "image=@path/to/test_photo.jpg" \
  -F "threshold=0.5" \
  -F "save_result_image=true"
```

### 3. Lấy danh sách người

```bash
curl -X GET "http://localhost:8000/api/persons/?skip=0&limit=10"
```

## Cấu trúc thư mục

```
face_compare/
├── app/
│   ├── models/          # Pydantic models
│   ├── routers/         # API routes
│   ├── services/        # Business logic
│   ├── utils/           # Utilities
│   └── main.py          # FastAPI app
├── public/
│   └── images/          # Lưu trữ ảnh
├── requirements.txt     # Dependencies
├── run.py              # Entry point
├── Dockerfile          # Docker config
├── docker-compose.yml  # Docker Compose
└── README.md           # Documentation
```

## Cấu hình

### Ngưỡng nhận diện (threshold)
- `0.3`: Rất nghiêm ngặt (ít false positive)
- `0.5`: Cân bằng (mặc định)
- `0.7`: Thoải mái hơn (nhiều match hơn)

### Định dạng ảnh hỗ trợ
- JPEG (.jpg, .jpeg)
- PNG (.png)
- Kích thước tối đa: 10MB
- Tự động resize nếu > 1920x1080

## Monitoring và Logs

### Kiểm tra trạng thái API
```bash
curl http://localhost:8000/health
```

### Kiểm tra MongoDB
```bash
# Linux
sudo systemctl status mongod
sudo journalctl -u mongod

# macOS
brew services list | grep mongo
tail -f /usr/local/var/log/mongodb/mongo.log
```

### Thống kê
```bash
curl http://localhost:8000/api/persons/stats/summary
```

### Kết nối MongoDB shell
```bash
mongosh
use face_recognition_db
show collections
db.persons.find().limit(5)
```

## Troubleshooting

### Lỗi cài đặt face_recognition
```bash
# Ubuntu/Debian
sudo apt-get install build-essential cmake libopenblas-dev liblapack-dev libx11-dev libgtk-3-dev libdlib-dev

# macOS
brew install cmake boost boost-python3 dlib

# Nếu vẫn lỗi, thử cài từng package riêng:
pip install cmake
pip install dlib
pip install face-recognition
```

### Lỗi kết nối MongoDB
1. Kiểm tra MongoDB đang chạy: 
   - Linux: `sudo systemctl status mongod`
   - macOS: `brew services list | grep mongo`
2. Kiểm tra port 27017 có bị chiếm: `netstat -an | grep 27017`
3. Kiểm tra biến môi trường MONGODB_URL
4. Xem MongoDB logs:
   - Linux: `sudo journalctl -u mongod`
   - macOS: `tail -f /usr/local/var/log/mongodb/mongo.log`

### Lỗi không nhận diện được khuôn mặt
1. Đảm bảo ảnh có chất lượng tốt, ánh sáng đủ
2. Khuôn mặt phải rõ ràng, không bị che
3. Thử giảm threshold xuống 0.3-0.4

### Performance tối ưu
1. Sử dụng model "hog" cho tốc độ (mặc định)
2. Sử dụng model "cnn" cho độ chính xác (cần GPU)
3. Resize ảnh về kích thước nhỏ hơn nếu cần

## API Examples với Python

```python
import requests

# Đăng ký người mới
def register_person():
    url = "http://localhost:8000/api/persons/register"
    data = {
        "name": "Nguyễn Văn A",
        "email": "<EMAIL>",
        "phone": "**********"
    }
    files = {"image": open("photo.jpg", "rb")}
    response = requests.post(url, data=data, files=files)
    return response.json()

# Xác thực khuôn mặt
def verify_face():
    url = "http://localhost:8000/api/face-recognition/verify"
    data = {"threshold": 0.5, "save_result_image": True}
    files = {"image": open("test.jpg", "rb")}
    response = requests.post(url, data=data, files=files)
    return response.json()
```

## Security Notes

⚠️ **Lưu ý bảo mật cho Production:**

1. Cấu hình MongoDB authentication
2. Sử dụng HTTPS
3. Giới hạn CORS origins
4. Thêm authentication/authorization
5. Rate limiting
6. Input validation mạnh mẽ hơn
7. Firewall cho MongoDB port (27017)
8. MongoDB binding chỉ localhost nếu không cần remote access

## License

MIT License - Xem file LICENSE để biết thêm chi tiết.

## Support

- 📧 Email: <EMAIL>
- 🐛 Issues: GitHub Issues
- 📖 Docs: http://localhost:8000/docs