# 🚀 Hướng dẫn cài đặt Face Recognition API (Không Docker)

## 📋 Tổng quan

Service Face Recognition API được xây dựng với:
- **FastAPI** - Web framework hiện đại
- **MongoDB** - Database lưu trữ thông tin người dùng
- **face_recognition** - Th<PERSON> viện nhận diện khuôn mặt (miễn phí)
- **Python 3.8+** - Ngôn ngữ lập trình

## ⚡ Cách nhanh nhất (5 phút)

```bash
# 1. <PERSON> chuyển vào thư mục project
cd face_compare

# 2. Chạy script tự động (cài đặt tất cả)
bash run_all.sh
```

Script `run_all.sh` sẽ tự động:
- ✅ Kiểm tra Python3 và pip
- ✅ Cài đặt system dependencies
- ✅ Cài đặt MongoDB
- ✅ Tạo virtual environment
- ✅ Cài đặt Python packages
- ✅ Khởi động MongoDB
- ✅ Chạy API

## 📝 Y<PERSON><PERSON> c<PERSON><PERSON> hệ thống

### Tối thiểu:
- **OS**: Ubuntu 18.04+, macOS 10.14+, hoặc Windows 10 với WSL
- **RAM**: 2GB (khuyến nghị 4GB)
- **Disk**: 2GB trống
- **Python**: 3.8 hoặc mới hơn

### Khuyến nghị:
- **RAM**: 4GB+
- **CPU**: 2 cores+
- **SSD**: Để tăng tốc độ I/O

## 🔧 Cài đặt từng bước (Thủ công)

### Bước 1: Chuẩn bị môi trường

#### Ubuntu/Debian:
```bash
# Cập nhật system
sudo apt update && sudo apt upgrade -y

# Cài đặt Python và tools
sudo apt install -y \
    python3 \
    python3-pip \
    python3-venv \
    python3-dev \
    build-essential \
    cmake \
    pkg-config \
    libopenblas-dev \
    liblapack-dev \
    libjpeg-dev \
    libpng-dev \
    libtiff-dev \
    libdlib-dev \
    libblas-dev \
    software-properties-common \
    gnupg \
    curl \
    wget
```

#### macOS:
```bash
# Cài đặt Homebrew (nếu chưa có)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Cài đặt dependencies
brew install \
    python3 \
    cmake \
    boost \
    boost-python3 \
    pkg-config \
    dlib
```

### Bước 2: Cài đặt MongoDB

#### Ubuntu/Debian:
```bash
# Thêm MongoDB GPG key
curl -fsSL https://pgp.mongodb.com/server-7.0.asc | \
    sudo gpg -o /usr/share/keyrings/mongodb-server-7.0.gpg --dearmor

# Thêm MongoDB repository
echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-7.0.gpg ] https://repo.mongodb.org/apt/ubuntu $(lsb_release -cs)/mongodb-org/7.0 multiverse" | \
    sudo tee /etc/apt/sources.list.d/mongodb-org-7.0.list

# Cập nhật và cài đặt MongoDB
sudo apt-get update
sudo apt-get install -y mongodb-org

# Khởi động và enable MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# Kiểm tra trạng thái
sudo systemctl status mongod
```

#### macOS:
```bash
# Thêm MongoDB tap
brew tap mongodb/brew

# Cài đặt MongoDB Community Edition
brew install mongodb-community

# Khởi động MongoDB
brew services start mongodb/brew/mongodb-community

# Kiểm tra trạng thái
brew services list | grep mongo
```

### Bước 3: Setup Python Environment

```bash
# Tạo virtual environment
python3 -m venv venv

# Kích hoạt virtual environment
source venv/bin/activate  # Linux/macOS
# venv\Scripts\activate    # Windows

# Upgrade pip
pip install --upgrade pip setuptools wheel

# Cài đặt dependencies từng bước (để dễ debug)
pip install numpy==1.24.3
pip install cmake
pip install dlib
pip install face-recognition==1.3.0

# Cài đặt tất cả packages
pip install -r requirements.txt
```

### Bước 4: Tạo cấu hình

```bash
# Copy file cấu hình mẫu
cp .env.example .env

# Tạo thư mục cần thiết
mkdir -p public/images logs
```

### Bước 5: Kiểm tra cài đặt

```bash
# Kích hoạt virtual environment
source venv/bin/activate

# Chạy script kiểm tra
python check_requirements.py
```

### Bước 6: Khởi động service

```bash
# Đảm bảo MongoDB đang chạy
# Linux:
sudo systemctl status mongod

# macOS:
brew services list | grep mongo

# Chạy API
python run.py
```

## 🧪 Kiểm tra và Test

### Kiểm tra API hoạt động:
```bash
# Health check
curl http://localhost:8000/health

# Swagger UI
open http://localhost:8000/docs  # macOS
xdg-open http://localhost:8000/docs  # Linux
```

### Test với script tự động:
```bash
# Chạy test (cần có ảnh test)
python test_api.py --register-image path/to/photo.jpg --verify-image path/to/test.jpg
```

### Test thủ công với curl:
```bash
# Đăng ký người mới
curl -X POST "http://localhost:8000/api/persons/register" \
  -F "name=Test User" \
  -F "email=<EMAIL>" \
  -F "image=@path/to/photo.jpg"

# Xác thực khuôn mặt
curl -X POST "http://localhost:8000/api/face-recognition/verify" \
  -F "image=@path/to/test.jpg" \
  -F "threshold=0.5"
```

## 🔍 Troubleshooting

### Lỗi cài đặt face_recognition

**Trên Ubuntu/Debian:**
```bash
# Lỗi thiếu dependencies
sudo apt install build-essential cmake libboost-python-dev libdlib-dev

# Lỗi memory trong quá trình compile
export MAKEFLAGS="-j1"  # Compile single-threaded
pip install dlib
```

**Trên macOS:**
```bash
# Lỗi Xcode command line tools
xcode-select --install

# Lỗi boost
brew uninstall boost-python3
brew install boost-python3

# Compile với Homebrew paths
export CPPFLAGS=-I/opt/homebrew/include
export LDFLAGS=-L/opt/homebrew/lib
pip install dlib
```

### Lỗi MongoDB

**Không khởi động được:**
```bash
# Kiểm tra logs
# Linux:
sudo journalctl -u mongod -f

# macOS:
tail -f /opt/homebrew/var/log/mongodb/mongo.log

# Kiểm tra port conflict
sudo netstat -tlnp | grep :27017
```

**Permission denied:**
```bash
# Linux:
sudo chown -R mongodb:mongodb /var/lib/mongodb
sudo chown mongodb:mongodb /tmp/mongodb-27017.sock
sudo systemctl restart mongod

# macOS:
sudo chown -R $(whoami) /opt/homebrew/var/mongodb
brew services restart mongodb/brew/mongodb-community
```

### Lỗi Virtual Environment

```bash
# Xóa và tạo lại venv
rm -rf venv
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### Lỗi Port 8000 bị chiếm

```bash
# Tìm process đang dùng port
sudo lsof -i :8000

# Kill process (thay PID)
sudo kill -9 <PID>

# Hoặc chạy API với port khác
uvicorn app.main:app --port 8001
```

## 📊 Monitoring và Logs

### Kiểm tra MongoDB:
```bash
# Kết nối MongoDB shell
mongosh

# Trong MongoDB shell:
use face_recognition_db
show collections
db.persons.find().limit(5)
db.persons.count()
```

### Kiểm tra API logs:
```bash
# Chạy với verbose logging
uvicorn app.main:app --log-level debug

# Hoặc redirect logs
python run.py > logs/api.log 2>&1 &
tail -f logs/api.log
```

### System monitoring:
```bash
# CPU và Memory usage
htop

# Disk usage
df -h

# MongoDB stats
mongosh --eval "db.stats()"
```

## ⚙️ Cấu hình nâng cao

### Tối ưu MongoDB cho production:
```bash
# Tạo index cho tìm kiếm nhanh
mongosh face_recognition_db --eval '
db.persons.createIndex({"email": 1}, {unique: true, sparse: true});
db.persons.createIndex({"name": "text", "email": "text"});
db.persons.createIndex({"created_at": 1});
'
```

### Cấu hình face_recognition:
```python
# Trong app/services/face_recognition_service.py
# Thay đổi model để tăng tốc độ:
self.model = "hog"  # Nhanh hơn
# self.model = "cnn"  # Chính xác hơn nhưng cần GPU

# Thay đổi tolerance:
self.tolerance = 0.5  # Cân bằng
# self.tolerance = 0.3  # Nghiêm ngặt hơn
# self.tolerance = 0.7  # Thoải mái hơn
```

### Cấu hình API performance:
```python
# Trong run.py
uvicorn.run(
    "app.main:app",
    host="0.0.0.0",
    port=8000,
    workers=4,  # Tăng số workers
    reload=False,  # Tắt reload trong production
    access_log=False  # Tắt access log để tăng tốc
)
```

## 🔒 Security Notes

### MongoDB Security:
```bash
# Enable authentication (production)
sudo vi /etc/mongod.conf
# Thêm:
# security:
#   authorization: enabled

# Tạo admin user
mongosh --eval '
db.getSiblingDB("admin").createUser({
  user: "admin",
  pwd: "your_password",
  roles: ["userAdminAnyDatabase", "dbAdminAnyDatabase", "readWriteAnyDatabase"]
})
'
```

### API Security:
- Sử dụng HTTPS trong production
- Thêm rate limiting
- Validate input nghiêm ngặt
- Không expose MongoDB port ra internet
- Sử dụng authentication tokens

## 📚 Scripts có sẵn

- `run_all.sh` - Cài đặt và chạy toàn bộ tự động
- `setup.sh` - Chỉ cài đặt môi trường
- `start.sh` - Chỉ khởi động API
- `check_requirements.py` - Kiểm tra môi trường
- `test_api.py` - Test API endpoints
- `install_system_deps.sh` - Cài system dependencies

## 🎯 API Endpoints

| Method | Endpoint | Mô tả |
|--------|----------|-------|
| GET | `/` | Health check cơ bản |
| GET | `/health` | Health check chi tiết |
| GET | `/docs` | Swagger UI |
| POST | `/api/persons/register` | Đăng ký người mới |
| GET | `/api/persons/` | Danh sách người |
| GET | `/api/persons/{id}` | Chi tiết người |
| PUT | `/api/persons/{id}` | Cập nhật thông tin |
| DELETE | `/api/persons/{id}` | Xóa người |
| POST | `/api/face-recognition/verify` | Xác thực nhiều khuôn mặt |
| POST | `/api/face-recognition/verify-single` | Xác thực một khuôn mặt |
| GET | `/api/face-recognition/settings` | Cài đặt nhận diện |

## 🎉 Hoàn tất!

Sau khi hoàn thành setup, bạn có thể:

1. **Truy cập Swagger UI**: http://localhost:8000/docs
2. **Test API**: Sử dụng scripts có sẵn
3. **Monitor**: Kiểm tra logs và MongoDB
4. **Scale**: Tăng workers và optimize

Chúc bạn sử dụng API thành công! 🚀