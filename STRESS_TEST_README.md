# 🔥 Stress Test cho Face Recognition API

Hướng dẫn chi tiết để thực hiện stress test cho API `/face-recognition/verify`

## 📋 Tổng quan

Bộ công cụ stress test bao gồm:
- **simple_stress_test.py**: Stress test đơn giản với threading
- **stress_test_verify.py**: Stress test nâng cao với asyncio
- **prepare_test_images.py**: Chuẩn bị ảnh test
- **install_stress_test_deps.py**: Cài đặt dependencies

## 🚀 Hướng dẫn nhanh

### Bước 1: Cài đặt dependencies
```bash
python install_stress_test_deps.py
```

### Bước 2: Chuẩn bị ảnh test
```bash
python prepare_test_images.py
```

### Bước 3: Đ<PERSON>m bảo server đang chạy
```bash
python run.py
```

### Bước 4: Chạy stress test
```bash
# Stress test đơn giản
python simple_stress_test.py

# Hoặc stress test nâng cao
python stress_test_verify.py
```

## 📊 Các loại stress test

### 1. Simple Stress Test (Đơn giản)
- **File**: `simple_stress_test.py`
- **Công nghệ**: requests + threading
- **Ưu điểm**: Dễ hiểu, dễ debug
- **Phù hợp**: Test cơ bản, môi trường đơn giản

**Cấu hình mặc định:**
```python
CONCURRENT_USERS = 5      # 5 users đồng thời
REQUESTS_PER_USER = 3     # 3 requests/user
THRESHOLD = 0.5           # Ngưỡng nhận diện
```

### 2. Advanced Stress Test (Nâng cao)
- **File**: `stress_test_verify.py`
- **Công nghệ**: aiohttp + asyncio
- **Ưu điểm**: Hiệu suất cao, nhiều metrics
- **Phù hợp**: Test chuyên sâu, production

**Cấu hình mặc định:**
```python
CONCURRENT_USERS = 10     # 10 users đồng thời
REQUESTS_PER_USER = 5     # 5 requests/user
THRESHOLD = 0.5           # Ngưỡng nhận diện
```

## ⚙️ Cấu hình test

### Thay đổi cấu hình trong script:

```python
# Trong simple_stress_test.py hoặc stress_test_verify.py
BASE_URL = "http://localhost:8000"    # URL server
CONCURRENT_USERS = 10                 # Số user đồng thời
REQUESTS_PER_USER = 5                 # Số request/user
THRESHOLD = 0.5                       # Ngưỡng nhận diện

# Đường dẫn ảnh test
IMAGE_PATHS = [
    "public/images/test/test_image_001.jpg",
    "public/images/test/test_image_002.jpg",
    # Thêm ảnh khác...
]
```

### Các mức độ test khuyến nghị:

| Mức độ | Users | Requests/User | Tổng Requests | Mục đích |
|--------|-------|---------------|---------------|----------|
| Light  | 1-2   | 10           | 10-20         | Test cơ bản |
| Medium | 5-10  | 5            | 25-50         | Test thường |
| Heavy  | 10-20 | 3            | 30-60         | Test tải cao |
| Extreme| 20+   | 2            | 40+           | Test giới hạn |

## 📈 Metrics được đo

### 1. Tổng quan
- **Total Requests**: Tổng số requests
- **Successful Requests**: Số requests thành công
- **Failed Requests**: Số requests thất bại
- **Success Rate**: Tỷ lệ thành công (%)
- **Throughput**: Requests/giây

### 2. Hiệu suất
- **Average Response Time**: Thời gian phản hồi trung bình
- **Min/Max Response Time**: Thời gian nhanh nhất/chậm nhất
- **Median Response Time**: Thời gian phản hồi median
- **P95/P99**: Percentile 95% và 99%

### 3. Lỗi
- **Error Types**: Phân loại lỗi theo HTTP status
- **Error Count**: Số lượng từng loại lỗi

## 📁 Cấu trúc file kết quả

```json
{
  "test_info": {
    "timestamp": "2024-01-01T10:00:00",
    "endpoint": "http://localhost:8000/face-recognition/verify",
    "total_requests": 50
  },
  "analysis": {
    "summary": {
      "total_requests": 50,
      "successful_requests": 48,
      "failed_requests": 2,
      "success_rate_percent": 96.0,
      "throughput_rps": 5.2
    },
    "response_times": {
      "average_seconds": 1.234,
      "minimum_seconds": 0.856,
      "maximum_seconds": 3.421,
      "median_seconds": 1.156,
      "p95_seconds": 2.345,
      "p99_seconds": 3.123
    },
    "errors": {
      "HTTP_500": 2
    }
  },
  "detailed_results": [...]
}
```

## 🔧 Troubleshooting

### Lỗi thường gặp:

#### 1. "Không thể kết nối server"
```bash
# Kiểm tra server có chạy không
curl http://localhost:8000/docs

# Khởi động server
python run.py
```

#### 2. "Không tìm thấy ảnh test"
```bash
# Tạo ảnh test
python prepare_test_images.py

# Hoặc copy ảnh thủ công vào public/images/
```

#### 3. "Timeout errors"
- Giảm số concurrent users
- Tăng timeout trong script
- Kiểm tra tài nguyên server

#### 4. "Memory errors"
- Giảm số requests
- Kiểm tra RAM server
- Restart server

### Tối ưu hiệu suất:

#### Server side:
```python
# Trong main.py, tăng workers
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        workers=4  # Tăng số workers
    )
```

#### Client side:
```python
# Tăng connection pool
connector = aiohttp.TCPConnector(limit=100)
timeout = aiohttp.ClientTimeout(total=60)
```

## 📊 Phân tích kết quả

### Hiệu suất tốt:
- Success rate > 95%
- Average response time < 2s
- P95 response time < 5s
- Throughput > 5 req/s

### Hiệu suất cần cải thiện:
- Success rate < 90%
- Average response time > 5s
- Nhiều timeout errors
- Throughput < 2 req/s

### Hành động cải thiện:
1. **Tối ưu code**: Profile và optimize bottlenecks
2. **Tăng resources**: CPU, RAM, GPU
3. **Caching**: Cache face encodings
4. **Load balancing**: Sử dụng nhiều workers
5. **Database optimization**: Index, connection pooling

## 🎯 Best Practices

1. **Bắt đầu nhỏ**: Test với 1-2 users trước
2. **Tăng dần**: Tăng dần concurrent users
3. **Monitor resources**: Theo dõi CPU, RAM, GPU
4. **Test realistic**: Sử dụng ảnh thật
5. **Document results**: Lưu kết quả để so sánh
6. **Test regularly**: Chạy test định kỳ

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra logs server
2. Xem file kết quả JSON
3. Thử giảm tải test
4. Restart server và thử lại
