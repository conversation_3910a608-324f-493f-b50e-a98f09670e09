from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
import os
import logging

from .utils.database import connect_to_mongo, close_mongo_connection, health_check
from .routers import person_routes, face_recognition_routes

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Quản lý lifecycle của ứng dụng"""
    # Startup
    try:
        logger.info("Khởi động Face Recognition API...")
        await connect_to_mongo()
        logger.info("Kết nối MongoDB thành công")
        
        # Đảm bảo thư mục public tồn tại
        os.makedirs("public/images", exist_ok=True)
        logger.info("Tạo thư mục public/images thành công")
        
    except Exception as e:
        logger.error(f"Lỗi khi khởi động ứng dụng: {e}")
        raise e
    
    yield
    
    # Shutdown
    try:
        await close_mongo_connection()
        logger.info("Đóng kết nối MongoDB thành công")
    except Exception as e:
        logger.error(f"Lỗi khi đóng kết nối: {e}")


# Tạo FastAPI app
app = FastAPI(
    title="Face Recognition API",
    description="API xác thực khuôn mặt với FastAPI và MongoDB",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Trong production nên chỉ định cụ thể domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/public", StaticFiles(directory="public"), name="public")

# Include routers
app.include_router(person_routes.router)
app.include_router(face_recognition_routes.router)


@app.get("/", summary="Health Check")
async def root():
    """Endpoint kiểm tra trạng thái API"""
    return {
        "message": "Face Recognition API đang hoạt động",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }


@app.get("/health", summary="Health Check Chi Tiết")
async def health_check_endpoint():
    """Kiểm tra sức khỏe hệ thống"""
    try:
        # Kiểm tra kết nối database
        db_healthy = await health_check()
        
        if db_healthy:
            return {
                "status": "healthy",
                "services": {
                    "api": "running",
                    "database": "connected",
                    "face_recognition": "ready"
                },
                "message": "Tất cả dịch vụ đang hoạt động bình thường"
            }
        else:
            raise HTTPException(
                status_code=503,
                detail={
                    "status": "unhealthy",
                    "services": {
                        "api": "running",
                        "database": "disconnected",
                        "face_recognition": "unavailable"
                    },
                    "message": "Database không kết nối được"
                }
            )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail={
                "status": "unhealthy",
                "error": str(e),
                "message": "Lỗi khi kiểm tra sức khỏe hệ thống"
            }
        )