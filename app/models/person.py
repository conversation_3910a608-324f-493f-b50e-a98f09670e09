from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List, Any, Dict
from datetime import datetime
from bson import ObjectId


class PyObjectId(ObjectId):
    @classmethod
    def __get_pydantic_json_schema__(cls, _source_type: Any, _handler) -> dict:
        return {"type": "string"}

    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v, _info=None):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid objectid")
        return ObjectId(v)


class PersonBase(BaseModel):
    name: str = Field(..., description="Tên của người")
    student_id: str = Field(..., description="Mã số sinh viên")
    meta: Optional[Dict[str, Any]] = Field(None, description="Thông tin meta dạng JSON")


class PersonCreate(PersonBase):
    pass


class PersonUpdate(BaseModel):
    name: Optional[str] = None
    student_id: Optional[str] = None
    meta: Optional[Dict[str, Any]] = None


class Person(PersonBase):
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )

    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    image_path: str = Field(..., description="Đường dẫn ảnh")
    face_encoding: List[float] = Field(..., description="Mã hóa khuôn mặt")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class PersonResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    name: str
    student_id: str
    meta: Optional[Dict[str, Any]] = None
    image_path: str
    created_at: datetime


class FaceMatch(BaseModel):
    person_id: str
    name: str
    student_id: str
    meta: Optional[Dict[str, Any]] = None
    confidence: float = Field(..., description="Độ tin cậy (0-1)")
    distance: float = Field(..., description="Khoảng cách Euclidean")


class FaceRecognitionResult(BaseModel):
    total_faces_detected: int = Field(..., description="Số khuôn mặt phát hiện")
    matches: List[FaceMatch] = Field(..., description="Danh sách kết quả khớp")
    image_path: Optional[str] = Field(None, description="Đường dẫn ảnh kết quả")