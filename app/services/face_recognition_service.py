import face_recognition
import cv2
import numpy as np
from typing import List, Tuple, Optional
import os
from PIL import Image, ExifTags
import uuid
from datetime import datetime

class FaceRecognitionService:
    def __init__(self):
        self.tolerance = 0.5  # Ngưỡng nhận diện (thấp hơn = nghiêm ngặt hơn)
        self.model = "hog"  # C<PERSON> thể dùng "cnn" cho độ chính xác cao hơn nhưng chậm hơn

    def fix_image_orientation(self, image_path: str) -> str:
        """Sửa orientation của ảnh dựa trên EXIF data"""
        try:
            with Image.open(image_path) as img:
                # Kiểm tra EXIF orientation
                for orientation in ExifTags.TAGS.keys():
                    if ExifTags.TAGS[orientation] == 'Orientation':
                        break

                exif = img._getexif()
                if exif is not None:
                    orientation_value = exif.get(orientation)
                    if orientation_value:
                        if orientation_value == 3:
                            img = img.rotate(180, expand=True)
                        elif orientation_value == 6:
                            img = img.rotate(270, expand=True)
                        elif orientation_value == 8:
                            img = img.rotate(90, expand=True)

                        # Lưu ảnh đã sửa orientation
                        fixed_path = image_path.replace(".", "_fixed.")
                        img.save(fixed_path, quality=95)
                        return fixed_path

                return image_path

        except Exception as e:
            print(f"Lỗi khi sửa orientation: {e}")
            return image_path

    def try_multiple_orientations(self, image_path: str) -> Tuple[Optional[List[float]], str]:
        """Thử nhiều góc xoay để tìm khuôn mặt"""
        try:
            with Image.open(image_path) as img:
                # Thử các góc xoay khác nhau
                rotations = [0, 90, 180, 270]

                for rotation in rotations:
                    if rotation == 0:
                        test_img = img
                        test_path = image_path
                    else:
                        test_img = img.rotate(rotation, expand=True)
                        test_path = image_path.replace(".", f"_rot{rotation}.")
                        test_img.save(test_path, quality=95)

                    # Thử phát hiện khuôn mặt với góc xoay này
                    face_encoding = self._extract_face_encoding_from_path(test_path)

                    if face_encoding:
                        print(f"Phát hiện khuôn mặt với góc xoay {rotation} độ")
                        return face_encoding, test_path

                    # Xóa file tạm nếu không phải ảnh gốc
                    if rotation != 0 and os.path.exists(test_path):
                        os.remove(test_path)

                return None, image_path

        except Exception as e:
            print(f"Lỗi khi thử nhiều orientation: {e}")
            return None, image_path

    def _extract_face_encoding_from_path(self, image_path: str) -> Optional[List[float]]:
        """Helper method để trích xuất face encoding từ đường dẫn"""
        try:
            # Load ảnh
            image = face_recognition.load_image_file(image_path)

            # Tìm vị trí khuôn mặt với cả 2 model
            face_locations = face_recognition.face_locations(image, model=self.model)

            # Nếu không tìm thấy với model hiện tại, thử model khác
            if not face_locations and self.model == "hog":
                face_locations = face_recognition.face_locations(image, model="cnn")
            elif not face_locations and self.model == "cnn":
                face_locations = face_recognition.face_locations(image, model="hog")

            if not face_locations:
                return None

            # Lấy encoding của khuôn mặt đầu tiên
            face_encodings = face_recognition.face_encodings(image, face_locations)

            if face_encodings:
                return face_encodings[0].tolist()

            return None

        except Exception as e:
            print(f"Lỗi khi trích xuất face encoding: {e}")
            return None

    def extract_face_encoding(self, image_path: str) -> Optional[List[float]]:
        """Trích xuất mã hóa khuôn mặt từ ảnh với xử lý orientation"""
        try:
            # Bước 1: Thử sửa orientation dựa trên EXIF
            fixed_path = self.fix_image_orientation(image_path)

            # Bước 2: Thử phát hiện khuôn mặt với ảnh đã sửa orientation
            face_encoding = self._extract_face_encoding_from_path(fixed_path)

            if face_encoding:
                # Cleanup file tạm nếu có
                if fixed_path != image_path and os.path.exists(fixed_path):
                    os.remove(fixed_path)
                return face_encoding

            # Bước 3: Nếu vẫn không tìm thấy, thử nhiều góc xoay
            print("Không tìm thấy khuôn mặt, thử xoay ảnh...")
            face_encoding, best_path = self.try_multiple_orientations(image_path)

            # Cleanup files tạm
            if fixed_path != image_path and os.path.exists(fixed_path):
                os.remove(fixed_path)
            if best_path != image_path and os.path.exists(best_path):
                os.remove(best_path)

            return face_encoding

        except Exception as e:
            print(f"Lỗi khi trích xuất face encoding: {e}")
            return None

    def detect_faces_in_image(self, image_path: str) -> Tuple[List[np.ndarray], List[Tuple[int, int, int, int]]]:
        """Phát hiện tất cả khuôn mặt trong ảnh với xử lý orientation"""
        try:
            # Bước 1: Thử sửa orientation dựa trên EXIF
            fixed_path = self.fix_image_orientation(image_path)

            # Bước 2: Thử phát hiện khuôn mặt với ảnh đã sửa orientation
            face_encodings, face_locations = self._detect_faces_from_path(fixed_path)

            if face_encodings:
                # Cleanup file tạm nếu có
                if fixed_path != image_path and os.path.exists(fixed_path):
                    os.remove(fixed_path)
                return face_encodings, face_locations

            # Bước 3: Nếu vẫn không tìm thấy, thử nhiều góc xoay
            print("Không tìm thấy khuôn mặt, thử xoay ảnh...")
            best_encodings, best_locations, best_path = self._try_multiple_orientations_for_detection(image_path)

            # Cleanup files tạm
            if fixed_path != image_path and os.path.exists(fixed_path):
                os.remove(fixed_path)
            if best_path != image_path and os.path.exists(best_path):
                os.remove(best_path)

            return best_encodings, best_locations

        except Exception as e:
            print(f"Lỗi khi phát hiện khuôn mặt: {e}")
            return [], []

    def _detect_faces_from_path(self, image_path: str) -> Tuple[List[np.ndarray], List[Tuple[int, int, int, int]]]:
        """Helper method để phát hiện khuôn mặt từ đường dẫn"""
        try:
            # Load ảnh
            image = face_recognition.load_image_file(image_path)

            # Tìm vị trí khuôn mặt với model hiện tại
            face_locations = face_recognition.face_locations(image, model=self.model)

            # Nếu không tìm thấy với model hiện tại, thử model khác
            if not face_locations and self.model == "hog":
                face_locations = face_recognition.face_locations(image, model="cnn")
            elif not face_locations and self.model == "cnn":
                face_locations = face_recognition.face_locations(image, model="hog")

            # Trích xuất encodings
            face_encodings = face_recognition.face_encodings(image, face_locations)

            return face_encodings, face_locations

        except Exception as e:
            print(f"Lỗi khi phát hiện khuôn mặt từ path: {e}")
            return [], []

    def _try_multiple_orientations_for_detection(self, image_path: str) -> Tuple[List[np.ndarray], List[Tuple[int, int, int, int]], str]:
        """Thử nhiều góc xoay để phát hiện khuôn mặt"""
        try:
            with Image.open(image_path) as img:
                # Thử các góc xoay khác nhau
                rotations = [0, 90, 180, 270]

                for rotation in rotations:
                    if rotation == 0:
                        test_path = image_path
                    else:
                        test_img = img.rotate(rotation, expand=True)
                        test_path = image_path.replace(".", f"_rot{rotation}.")
                        test_img.save(test_path, quality=95)

                    # Thử phát hiện khuôn mặt với góc xoay này
                    face_encodings, face_locations = self._detect_faces_from_path(test_path)

                    if face_encodings:
                        print(f"Phát hiện {len(face_encodings)} khuôn mặt với góc xoay {rotation} độ")
                        return face_encodings, face_locations, test_path

                    # Xóa file tạm nếu không phải ảnh gốc
                    if rotation != 0 and os.path.exists(test_path):
                        os.remove(test_path)

                return [], [], image_path

        except Exception as e:
            print(f"Lỗi khi thử nhiều orientation cho detection: {e}")
            return [], [], image_path

    def compare_faces(self, known_encodings: List[List[float]], face_encoding: List[float]) -> Tuple[List[bool], List[float]]:
        """So sánh khuôn mặt với database"""
        try:
            # Chuyển đổi về numpy array
            known_encodings_np = [np.array(encoding) for encoding in known_encodings]
            face_encoding_np = np.array(face_encoding)

            # So sánh
            matches = face_recognition.compare_faces(known_encodings_np, face_encoding_np, tolerance=self.tolerance)
            distances = face_recognition.face_distance(known_encodings_np, face_encoding_np)

            return matches, distances.tolist()

        except Exception as e:
            print(f"Lỗi khi so sánh khuôn mặt: {e}")
            return [], []

    def save_image_with_boxes(self, image_path: str, face_locations: List[Tuple[int, int, int, int]],
                             matches: List[str], output_dir: str = "public/images") -> str:
        """Lưu ảnh với khung đánh dấu khuôn mặt với xử lý orientation"""
        try:
            # Bước 1: Sửa orientation của ảnh trước khi load bằng OpenCV
            fixed_path = self.fix_image_orientation(image_path)

            # Bước 2: Load ảnh bằng OpenCV
            image = cv2.imread(fixed_path)

            if image is None:
                print(f"Không thể load ảnh: {fixed_path}")
                return ""

            # Bước 3: Vẽ khung cho mỗi khuôn mặt
            for i, (top, right, bottom, left) in enumerate(face_locations):
                # Chọn màu dựa trên kết quả nhận diện
                color = (0, 255, 0) if i < len(matches) and matches[i] else (0, 0, 255)

                # Vẽ khung
                cv2.rectangle(image, (left, top), (right, bottom), color, 2)

                # Thêm nhãn
                label = matches[i] if i < len(matches) and matches[i] else "Unknown"
                cv2.putText(image, label, (left, top - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

            # Bước 4: Tạo tên file unique
            filename = f"result_{uuid.uuid4().hex[:8]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
            output_path = os.path.join(output_dir, filename)

            # Tạo thư mục nếu chưa tồn tại
            os.makedirs(output_dir, exist_ok=True)

            # Bước 5: Lưu ảnh
            cv2.imwrite(output_path, image)

            # Bước 6: Cleanup file tạm nếu có
            if fixed_path != image_path and os.path.exists(fixed_path):
                os.remove(fixed_path)

            return output_path

        except Exception as e:
            print(f"Lỗi khi lưu ảnh với khung: {e}")
            return ""

    def validate_image(self, image_path: str) -> bool:
        """Kiểm tra tính hợp lệ của ảnh"""
        try:
            with Image.open(image_path) as img:
                # Kiểm tra format
                print("Image format: ", img.format)
                # MPO là định dạng được sử dụng bởi iPhone và một số camera
                # Nó tương thích với JPEG và có thể được xử lý như JPEG
                if img.format not in ['JPEG', 'PNG', 'JPG', 'MPO']:
                    return False

                # Kiểm tra kích thước (không quá lớn)
                if img.width > 4000 or img.height > 4000:
                    return False

                return True

        except Exception:
            return False

    def resize_image_if_needed(self, image_path: str, max_width: int = 1920, max_height: int = 1080) -> str:
        """Resize ảnh nếu quá lớn với xử lý orientation"""
        try:
            # Bước 1: Sửa orientation trước khi resize
            fixed_path = self.fix_image_orientation(image_path)

            # Bước 2: Load ảnh đã sửa orientation
            with Image.open(fixed_path) as img:
                if img.width <= max_width and img.height <= max_height:
                    # Nếu không cần resize, cleanup file tạm và return
                    if fixed_path != image_path and os.path.exists(fixed_path):
                        # Copy ảnh đã sửa orientation về tên gốc nếu cần
                        if fixed_path != image_path:
                            img.save(image_path, quality=95)
                            os.remove(fixed_path)
                    return image_path

                # Bước 3: Tính tỷ lệ resize
                ratio = min(max_width / img.width, max_height / img.height)
                new_size = (int(img.width * ratio), int(img.height * ratio))

                # Bước 4: Resize và lưu
                resized_img = img.resize(new_size, Image.Resampling.LANCZOS)
                resized_path = image_path.replace(".", "_resized.")
                resized_img.save(resized_path, quality=95)

                # Bước 5: Cleanup file tạm
                if fixed_path != image_path and os.path.exists(fixed_path):
                    os.remove(fixed_path)

                return resized_path

        except Exception as e:
            print(f"Lỗi khi resize ảnh: {e}")
            return image_path