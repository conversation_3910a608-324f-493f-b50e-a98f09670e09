from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import ConnectionFailure
import os
from typing import Optional
import asyncio

class MongoDB:
    def __init__(self):
        self.client: Optional[AsyncIOMotorClient] = None
        self.database = None
        self._is_connected = False

mongo_db = MongoDB()

async def connect_to_mongo():
    """Kết nối đến MongoDB"""
    try:
        mongo_url = os.getenv("MONGODB_URL", "mongodb://localhost:27017")
        database_name = os.getenv("DATABASE_NAME", "face_recognition_db")
        
        mongo_db.client = AsyncIOMotorClient(
            mongo_url,
            serverSelectionTimeoutMS=5000,
            connectTimeoutMS=5000,
            socketTimeoutMS=5000
        )
        mongo_db.database = mongo_db.client[database_name]
        
        # Test connection with ping
        await asyncio.wait_for(
            mongo_db.client.admin.command('ping'), 
            timeout=5.0
        )
        
        mongo_db._is_connected = True
        print(f"Kết nối MongoDB thành công tại: {mongo_url}")
        
    except asyncio.TimeoutError:
        print("Lỗi: Timeout khi kết nối MongoDB")
        raise ConnectionError("MongoDB connection timeout")
    except ConnectionFailure as e:
        print(f"Lỗi kết nối MongoDB: {e}")
        mongo_db._is_connected = False
        raise e
    except Exception as e:
        print(f"Lỗi không xác định khi kết nối MongoDB: {e}")
        mongo_db._is_connected = False
        raise e

async def close_mongo_connection():
    """Đóng kết nối MongoDB"""
    if mongo_db.client is not None:
        mongo_db.client.close()
        mongo_db._is_connected = False
        print("Đã đóng kết nối MongoDB")

def get_database():
    """Lấy database instance"""
    if mongo_db.database is None:
        raise ConnectionError("Database chưa được khởi tạo")
    if not mongo_db._is_connected:
        raise ConnectionError("Database không được kết nối")
    return mongo_db.database

def get_collection(collection_name: str):
    """Lấy collection từ database"""
    database = get_database()
    return database[collection_name]

def is_connected() -> bool:
    """Kiểm tra trạng thái kết nối"""
    return mongo_db._is_connected and mongo_db.client is not None

async def health_check() -> bool:
    """Kiểm tra sức khỏe kết nối database"""
    try:
        if not is_connected():
            return False
        
        await asyncio.wait_for(
            mongo_db.client.admin.command('ping'),
            timeout=3.0
        )
        return True
    except Exception:
        return False