import os
import uuid
import aiofiles
from typing import Optional
from fastapi import UploadFile
from datetime import datetime


def generate_unique_filename(original_filename: str, prefix: str = "") -> str:
    """Tạo tên file unique"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    unique_id = uuid.uuid4().hex[:8]
    file_extension = os.path.splitext(original_filename)[1]
    print(f"File extension: {file_extension}")
    print(f"File name: {original_filename}")
    
    if prefix:
        return f"{prefix}_{timestamp}_{unique_id}{file_extension}"
    return f"{timestamp}_{unique_id}{file_extension}"


async def save_upload_file(upload_file: UploadFile, directory: str) -> str:
    """Lưu file upload vào thư mục chỉ định"""
    # Tạo thư mục nếu chưa tồn tại
    os.makedirs(directory, exist_ok=True)
    
    # Tạo tên file unique
    filename = generate_unique_filename(upload_file.filename, "person")
    file_path = os.path.join(directory, filename)
    
    # Lưu file
    async with aiofiles.open(file_path, 'wb') as f:
        content = await upload_file.read()
        await f.write(content)
    
    return file_path


def validate_file_type(filename: str, allowed_extensions: list = None) -> bool:
    """Kiểm tra loại file có hợp lệ không"""
    if allowed_extensions is None:
        allowed_extensions = ['.jpg', '.jpeg', '.png']
    
    file_extension = os.path.splitext(filename)[1].lower()
    return file_extension in allowed_extensions


def validate_file_size(file_size: int, max_size_mb: int = 10) -> bool:
    """Kiểm tra kích thước file"""
    max_size_bytes = max_size_mb * 1024 * 1024
    return file_size <= max_size_bytes


def delete_file(file_path: str) -> bool:
    """Xóa file khỏi hệ thống"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            return True
        return False
    except Exception as e:
        print(f"Lỗi khi xóa file {file_path}: {e}")
        return False


def get_file_size(file_path: str) -> Optional[int]:
    """Lấy kích thước file"""
    try:
        return os.path.getsize(file_path)
    except OSError:
        return None


def ensure_directory_exists(directory: str) -> bool:
    """Đảm bảo thư mục tồn tại"""
    try:
        os.makedirs(directory, exist_ok=True)
        return True
    except Exception as e:
        print(f"Lỗi khi tạo thư mục {directory}: {e}")
        return False