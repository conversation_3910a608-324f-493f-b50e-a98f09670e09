#!/usr/bin/env python3
"""
Script kiểm tra requirements và môi trường cho Face Recognition API
Chạy script này để đảm bảo tất cả dependencies đã được cài đặt đúng
"""

import sys
import subprocess
import importlib
import socket
import os
from typing import List, Tuple, Dict

class RequirementChecker:
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.success_count = 0
        self.total_checks = 0

    
    def check_python_version(self) -> bool:
        """Kiểm tra phiên bản Python"""
        self.total_checks += 1
        version = sys.version_info
        
        if version.major == 3 and version.minor >= 8:
            print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")
            self.success_count += 1
            return True
        else:
            error_msg = f"❌ Python {version.major}.{version.minor}.{version.micro} - Cần Python 3.8+"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    def check_virtual_environment(self) -> bool:
        """Kiểm tra có đang trong virtual environment không"""
        self.total_checks += 1
        
        if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            print("✅ Đang sử dụng virtual environment - OK")
            self.success_count += 1
            return True
        else:
            warning_msg = "⚠️ Không sử dụng virtual environment - Khuyến nghị sử dụng venv"
            print(warning_msg)
            self.warnings.append(warning_msg)
            return False
    
    def check_package(self, package_name: str, import_name: str = None) -> bool:
        """Kiểm tra package Python có cài đặt không"""
        self.total_checks += 1
        
        if import_name is None:
            import_name = package_name
        
        try:
            importlib.import_module(import_name)
            print(f"✅ {package_name} - OK")
            self.success_count += 1
            return True
        except ImportError:
            error_msg = f"❌ {package_name} - Chưa cài đặt"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    def check_system_command(self, command: str) -> bool:
        """Kiểm tra system command có tồn tại không"""
        self.total_checks += 1
        
        try:
            result = subprocess.run(['which', command], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {command} - OK")
                self.success_count += 1
                return True
            else:
                error_msg = f"❌ {command} - Không tìm thấy"
                print(error_msg)
                self.errors.append(error_msg)
                return False
        except Exception:
            error_msg = f"❌ {command} - Lỗi kiểm tra"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    def check_port(self, host: str, port: int, service_name: str) -> bool:
        """Kiểm tra port có đang mở không"""
        self.total_checks += 1
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"✅ {service_name} ({host}:{port}) - Đang chạy")
                self.success_count += 1
                return True
            else:
                error_msg = f"❌ {service_name} ({host}:{port}) - Không kết nối được"
                print(error_msg)
                self.errors.append(error_msg)
                return False
        except Exception as e:
            error_msg = f"❌ {service_name} ({host}:{port}) - Lỗi: {str(e)}"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    def check_directory(self, path: str, description: str) -> bool:
        """Kiểm tra thư mục có tồn tại không"""
        self.total_checks += 1
        
        if os.path.exists(path) and os.path.isdir(path):
            print(f"✅ {description} ({path}) - OK")
            self.success_count += 1
            return True
        else:
            warning_msg = f"⚠️ {description} ({path}) - Không tồn tại"
            print(warning_msg)
            self.warnings.append(warning_msg)
            return False
    
    def check_file(self, path: str, description: str) -> bool:
        """Kiểm tra file có tồn tại không"""
        self.total_checks += 1
        
        if os.path.exists(path) and os.path.isfile(path):
            print(f"✅ {description} ({path}) - OK")
            self.success_count += 1
            return True
        else:
            error_msg = f"❌ {description} ({path}) - Không tồn tại"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    def run_all_checks(self):
        """Chạy tất cả các kiểm tra"""
        print("🔍 Bắt đầu kiểm tra môi trường Face Recognition API")
        print("=" * 60)
        
        # Kiểm tra Python
        print("\n📋 Kiểm tra Python và Virtual Environment:")
        self.check_python_version()
        self.check_virtual_environment()
        
        # Kiểm tra system commands
        print("\n🛠️ Kiểm tra System Commands:")
        self.check_system_command('python3')
        self.check_system_command('pip3')
        self.check_system_command('mongod')
        
        # Kiểm tra Python packages chính
        print("\n📦 Kiểm tra Python Packages:")
        required_packages = [
            ('fastapi', 'fastapi'),
            ('uvicorn', 'uvicorn'),
            ('motor', 'motor'),
            ('pymongo', 'pymongo'),
            ('pydantic', 'pydantic'),
            ('face-recognition', 'face_recognition'),
            ('opencv-python', 'cv2'),
            ('numpy', 'numpy'),
            ('Pillow', 'PIL'),
            ('aiofiles', 'aiofiles'),
            ('python-multipart', 'multipart')
        ]
        
        for package_name, import_name in required_packages:
            self.check_package(package_name, import_name)
        
        # Kiểm tra services
        print("\n🌐 Kiểm tra Services:")
        self.check_port('localhost', 27017, 'MongoDB')
        
        # Kiểm tra thư mục và files
        print("\n📁 Kiểm tra Files và Directories:")
        self.check_file('requirements.txt', 'Requirements file')
        self.check_file('run.py', 'Main run script')
        self.check_file('app/main.py', 'FastAPI main app')
        self.check_directory('public/images', 'Public images directory')
        self.check_directory('app', 'App directory')
        
        # Tóm tắt kết quả
        self.print_summary()
    
    def print_summary(self):
        """In tóm tắt kết quả"""
        print("\n" + "=" * 60)
        print("📊 KẾT QUẢ KIỂM TRA")
        print("=" * 60)
        
        print(f"✅ Thành công: {self.success_count}/{self.total_checks}")
        print(f"❌ Lỗi: {len(self.errors)}")
        print(f"⚠️ Cảnh báo: {len(self.warnings)}")
        
        if self.errors:
            print("\n❌ CÁC LỖI CẦN KHẮC PHỤC:")
            for i, error in enumerate(self.errors, 1):
                print(f"  {i}. {error}")
        
        if self.warnings:
            print("\n⚠️ CÁC CẢNH BÁO:")
            for i, warning in enumerate(self.warnings, 1):
                print(f"  {i}. {warning}")
        
        print("\n" + "=" * 60)
        
        if len(self.errors) == 0:
            print("🎉 TẤT CẢ KIỂM TRA THÀNH CÔNG!")
            print("Bạn có thể chạy API với lệnh: python run.py")
            return True
        else:
            print("💡 HƯỚNG DẪN KHẮC PHỤC:")
            print("1. Cài đặt các packages bị thiếu:")
            print("   pip install -r requirements.txt")
            print("2. Khởi động MongoDB:")
            print("   Linux: sudo systemctl start mongod")
            print("   macOS: brew services start mongodb/brew/mongodb-community")
            print("3. Chạy lại script này để kiểm tra: python check_requirements.py")
            return False

def main():
    """Main function"""
    checker = RequirementChecker()
    success = checker.run_all_checks()
    
    # Exit với code phù hợp
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()