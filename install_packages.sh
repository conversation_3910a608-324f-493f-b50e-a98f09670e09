#!/bin/bash

# Script cài đặt packages từng bước để tránh lỗi Python 3.13
# Usage: bash install_packages.sh

set -e

echo "🔧 Cài đặt Python packages từng bước cho Python 3.13"
echo "=================================================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if in virtual environment
if [[ "$VIRTUAL_ENV" == "" ]]; then
    print_error "Vui lòng kích hoạt virtual environment trước!"
    echo "source venv/bin/activate"
    exit 1
fi

print_status "Kiểm tra Python version..."
python_version=$(python --version 2>&1 | cut -d' ' -f2)
echo "Python version: $python_version"

# Upgrade pip and essential tools
print_status "Upgrade pip và essential tools..."
pip install --upgrade pip==24.0
pip install --upgrade setuptools==69.5.1
pip install --upgrade wheel==0.42.0

# Install build dependencies first
print_status "Cài đặt build dependencies..."
pip install --upgrade build
pip install --upgrade packaging

# Install numpy first (required by many packages)
print_status "Cài đặt numpy..."
pip install "numpy>=1.24.0,<2.0.0"

# Install cmake
print_status "Cài đặt cmake..."
pip install cmake

# Install dlib with specific version
print_status "Cài đặt dlib (có thể mất vài phút)..."
export CMAKE_BUILD_PARALLEL_LEVEL=1
pip install --no-cache-dir "dlib>=19.24.0"

# Install face_recognition
print_status "Cài đặt face_recognition..."
pip install --no-cache-dir "face_recognition>=1.3.0"

# Install OpenCV
print_status "Cài đặt OpenCV..."
pip install "opencv-python>=4.8.0"

# Install Pillow
print_status "Cài đặt Pillow..."
pip install "Pillow>=10.0.0"

# Install FastAPI ecosystem
print_status "Cài đặt FastAPI ecosystem..."
pip install "fastapi>=0.104.0"
pip install "uvicorn[standard]>=0.24.0"
pip install "python-multipart>=0.0.6"

# Install database drivers
print_status "Cài đặt database drivers..."
pip install "motor>=3.3.0"
pip install "pymongo>=4.6.0"

# Install Pydantic
print_status "Cài đặt Pydantic..."
pip install "pydantic>=2.5.0"

# Install security packages
print_status "Cài đặt security packages..."
pip install "python-jose[cryptography]>=3.3.0"
pip install "passlib[bcrypt]>=1.7.4"

# Install file handling
print_status "Cài đặt file handling..."
pip install "aiofiles>=23.2.0"

# Install additional packages that might be needed
print_status "Cài đặt additional packages..."
pip install "requests>=2.31.0"
pip install "python-dotenv>=1.0.0"

# Verify installation
print_status "Kiểm tra cài đặt..."
python -c "
import sys
try:
    import fastapi
    print('✅ FastAPI:', fastapi.__version__)
    
    import face_recognition
    print('✅ face_recognition: OK')
    
    import cv2
    print('✅ OpenCV:', cv2.__version__)
    
    import numpy as np
    print('✅ NumPy:', np.__version__)
    
    import pymongo
    print('✅ PyMongo:', pymongo.__version__)
    
    import motor
    print('✅ Motor: OK')
    
    import pydantic
    print('✅ Pydantic:', pydantic.__version__)
    
    import PIL
    print('✅ Pillow:', PIL.__version__)
    
    print('\\n🎉 Tất cả packages đã được cài đặt thành công!')
    
except ImportError as e:
    print(f'❌ Lỗi import: {e}')
    sys.exit(1)
"

if [ $? -eq 0 ]; then
    print_success "Cài đặt hoàn tất!"
    echo ""
    echo "Bây giờ bạn có thể chạy:"
    echo "  python run.py"
else
    print_error "Có lỗi trong quá trình cài đặt"
    exit 1
fi