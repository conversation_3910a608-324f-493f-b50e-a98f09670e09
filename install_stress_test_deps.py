#!/usr/bin/env python3
"""
Script cài đặt dependencies cho stress testing
"""

import subprocess
import sys
import os

def install_package(package):
    """Cài đặt package bằng pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def check_package(package):
    """Kiểm tra package đã cài chưa"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    print("🔧 CÀI ĐẶT DEPENDENCIES CHO STRESS TESTING")
    print("="*50)
    
    # Danh sách packages cần thiết
    packages = [
        ("aiohttp", "aiohttp"),
        ("requests", "requests"),
        ("pillow", "PIL")  # (pip_name, import_name)
    ]
    
    for pip_name, import_name in packages:
        print(f"📦 Kiểm tra {pip_name}...")
        
        if check_package(import_name):
            print(f"   ✅ {pip_name} đã được cài đặt")
        else:
            print(f"   📥 Đang cài đặt {pip_name}...")
            if install_package(pip_name):
                print(f"   ✅ Cài đặt {pip_name} thành công")
            else:
                print(f"   ❌ Lỗi cài đặt {pip_name}")
    
    print("\n🎯 KIỂM TRA HOÀN TẤT")
    
    # Kiểm tra server
    print("\n🔍 Kiểm tra server...")
    try:
        import requests
        response = requests.get("http://localhost:8000/docs", timeout=5)
        if response.status_code == 200:
            print("   ✅ Server đang chạy tại http://localhost:8000")
        else:
            print("   ⚠️  Server có vấn đề")
    except:
        print("   ❌ Server chưa chạy hoặc không thể kết nối")
        print("   💡 Chạy server bằng: python run.py")
    
    # Kiểm tra ảnh test
    print("\n📸 Kiểm tra ảnh test...")
    test_dirs = ["public/images", "public/images/test"]
    found_images = False
    
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            images = [f for f in os.listdir(test_dir) 
                     if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            if images:
                print(f"   ✅ Tìm thấy {len(images)} ảnh trong {test_dir}")
                found_images = True
    
    if not found_images:
        print("   ❌ Không tìm thấy ảnh test")
        print("   💡 Chạy: python prepare_test_images.py")
    
    print("\n🚀 SẴN SÀNG STRESS TEST!")
    print("="*50)
    print("📋 Các lệnh có thể chạy:")
    print("1. python prepare_test_images.py     # Chuẩn bị ảnh test")
    print("2. python simple_stress_test.py      # Stress test đơn giản")
    print("3. python stress_test_verify.py      # Stress test nâng cao")

if __name__ == "__main__":
    main()
