#!/bin/bash

# Script cài đặt dependencies hệ thống cho Face Recognition API
# Chạy với quyền sudo: sudo bash install_system_deps.sh

echo "🔧 Cài đặt dependencies hệ thống cho Face Recognition API..."

# Detect OS
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "Phát hiện Linux OS..."
    
    # Update package list
    echo "📦 Cập nhật package list..."
    apt-get update
    
    # Install build tools
    echo "🛠️ Cài đặt build tools..."
    apt-get install -y \
        build-essential \
        cmake \
        pkg-config \
        libx11-dev \
        libatlas-base-dev \
        libgtk-3-dev \
        libboost-python-dev \
        libboost-system-dev \
        libboost-filesystem-dev \
        python3-dev \
        python3-pip \
        python3-venv \
        wget \
        curl \
        software-properties-common \
        gnupg \
        lsb-release
    
    # Install OpenCV dependencies
    echo "📷 Cài đặt OpenCV dependencies..."
    apt-get install -y \
        libopencv-dev \
        python3-opencv \
        libopenblas-dev \
        liblapack-dev \
        libblas-dev \
        libhdf5-dev \
        libhdf5-serial-dev \
        libhdf5-103 \
        libjpeg-dev \
        libpng-dev \
        libtiff-dev \
        libavcodec-dev \
        libavformat-dev \
        libswscale-dev \
        libv4l-dev \
        libxvidcore-dev \
        libx264-dev
    
    # Install dlib dependencies
    echo "🎯 Cài đặt dlib dependencies..."
    apt-get install -y \
        libdlib-dev \
        libblas-dev \
        liblapack-dev \
        libjpeg62-turbo-dev \
        libopenblas-dev
    
    # Install MongoDB
    echo "🗄️ Cài đặt MongoDB..."
    curl -fsSL https://pgp.mongodb.com/server-7.0.asc | \
        gpg -o /usr/share/keyrings/mongodb-server-7.0.gpg --dearmor
    
    echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-7.0.gpg ] https://repo.mongodb.org/apt/ubuntu $(lsb_release -cs)/mongodb-org/7.0 multiverse" | \
        tee /etc/apt/sources.list.d/mongodb-org-7.0.list
    
    apt-get update
    apt-get install -y mongodb-org
    
    # Enable and start MongoDB
    systemctl enable mongod
    systemctl start mongod
    
    echo "✅ Hoàn thành cài đặt dependencies cho Linux!"

elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo "Phát hiện macOS..."
    
    # Check if Homebrew is installed
    if ! command -v brew &> /dev/null; then
        echo "📦 Cài đặt Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    fi
    
    echo "🛠️ Cài đặt dependencies với Homebrew..."
    brew update
    brew install \
        cmake \
        boost \
        boost-python3 \
        opencv \
        dlib \
        pkg-config \
        python3
    
    echo "🗄️ Cài đặt MongoDB..."
    brew tap mongodb/brew
    brew install mongodb-community
    brew services start mongodb/brew/mongodb-community
    
    echo "✅ Hoàn thành cài đặt dependencies cho macOS!"

else
    echo "❌ OS không được hỗ trợ: $OSTYPE"
    echo "Vui lòng cài đặt dependencies thủ công:"
    echo "- Python 3.8+"
    echo "- cmake"
    echo "- dlib"
    echo "- OpenCV"
    echo "- boost"
    echo "- MongoDB"
    exit 1
fi

echo ""
echo "🎉 Cài đặt dependencies hệ thống hoàn thành!"
echo "Bây giờ bạn có thể chạy:"
echo "  python3 -m venv venv"
echo "  source venv/bin/activate"
echo "  pip install -r requirements.txt"
echo "  python run.py"
echo ""