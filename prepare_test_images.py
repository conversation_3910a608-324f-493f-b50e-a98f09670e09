#!/usr/bin/env python3
"""
Script để chuẩn bị ảnh test cho stress testing
Tạo nhiều bản copy của ảnh có sẵn với tên khác nhau
"""

import os
import shutil
from PIL import Image
import random

def create_test_images(source_dir: str = "public/images", 
                      output_dir: str = "public/images/test",
                      num_copies: int = 10):
    """Tạo nhiều ảnh test từ ảnh có sẵn"""
    
    # Tạo thư mục output
    os.makedirs(output_dir, exist_ok=True)
    
    # Tìm ảnh có sẵn
    image_extensions = ['.jpg', '.jpeg', '.png', '.JPG', '.JPEG', '.PNG']
    source_images = []
    
    if os.path.exists(source_dir):
        for file in os.listdir(source_dir):
            if any(file.endswith(ext) for ext in image_extensions):
                source_images.append(os.path.join(source_dir, file))
    
    if not source_images:
        print(f"❌ Không tìm thấy ảnh nào trong {source_dir}")
        return []
    
    print(f"✅ Tìm thấy {len(source_images)} ảnh nguồn")
    
    created_images = []
    
    for i in range(num_copies):
        # Chọn ảnh nguồn ngẫu nhiên
        source_image = random.choice(source_images)
        
        # Tạo tên file mới
        base_name = f"test_image_{i+1:03d}.jpg"
        output_path = os.path.join(output_dir, base_name)
        
        try:
            # Copy và có thể resize nhẹ để tạo sự khác biệt
            with Image.open(source_image) as img:
                # Resize ngẫu nhiên từ 80% đến 100% kích thước gốc
                scale = random.uniform(0.8, 1.0)
                new_width = int(img.width * scale)
                new_height = int(img.height * scale)
                
                if scale < 1.0:
                    img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                # Lưu với chất lượng ngẫu nhiên
                quality = random.randint(85, 95)
                img.save(output_path, 'JPEG', quality=quality)
                
                created_images.append(output_path)
                print(f"📸 Tạo: {base_name}")
                
        except Exception as e:
            print(f"❌ Lỗi tạo {base_name}: {e}")
    
    print(f"✅ Đã tạo {len(created_images)} ảnh test trong {output_dir}")
    return created_images

def create_sample_config():
    """Tạo file config mẫu cho stress test"""
    config = {
        "stress_test_config": {
            "base_url": "http://localhost:8000",
            "concurrent_users": [1, 5, 10, 20],  # Test với các mức độ khác nhau
            "requests_per_user": 5,
            "threshold": 0.5,
            "test_scenarios": [
                {
                    "name": "Light Load",
                    "concurrent_users": 1,
                    "requests_per_user": 10
                },
                {
                    "name": "Medium Load", 
                    "concurrent_users": 5,
                    "requests_per_user": 5
                },
                {
                    "name": "Heavy Load",
                    "concurrent_users": 10,
                    "requests_per_user": 3
                },
                {
                    "name": "Extreme Load",
                    "concurrent_users": 20,
                    "requests_per_user": 2
                }
            ]
        }
    }
    
    import json
    with open("stress_test_config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    print("📝 Đã tạo stress_test_config.json")

def main():
    print("🔧 CHUẨN BỊ ẢNH TEST CHO STRESS TESTING")
    print("="*50)
    
    # Tạo ảnh test
    test_images = create_test_images(num_copies=15)
    
    if test_images:
        print(f"\n📋 Danh sách ảnh test đã tạo:")
        for img in test_images[:5]:  # Hiển thị 5 ảnh đầu
            print(f"   {img}")
        if len(test_images) > 5:
            print(f"   ... và {len(test_images) - 5} ảnh khác")
        
        # Tạo config mẫu
        create_sample_config()
        
        print(f"\n🚀 HƯỚNG DẪN CHẠY STRESS TEST:")
        print("1. Đảm bảo server đang chạy:")
        print("   python run.py")
        print("\n2. Chạy stress test đơn giản:")
        print("   python simple_stress_test.py")
        print("\n3. Hoặc chạy stress test nâng cao:")
        print("   python stress_test_verify.py")
        
        print(f"\n📊 Cập nhật đường dẫn ảnh trong script:")
        print("IMAGE_PATHS = [")
        for img in test_images[:3]:
            print(f'    "{img}",')
        print("    # ... thêm ảnh khác")
        print("]")
        
    else:
        print("❌ Không thể tạo ảnh test")
        print("💡 Hướng dẫn:")
        print("1. Thêm ít nhất 1 ảnh vào thư mục public/images/")
        print("2. Chạy lại script này")

if __name__ == "__main__":
    main()
