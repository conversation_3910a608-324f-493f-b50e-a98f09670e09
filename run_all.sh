#!/bin/bash

# Script chạy toàn bộ Face Recognition API (không sử dụng Docker)
# Usage: bash run_all.sh

set -e

echo "🚀 Face Recognition API - Complete Setup and Run"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "Kiểm tra prerequisites..."
    
    if ! command_exists python3; then
        print_error "Python3 chưa được cài đặt!"
        echo "Ubuntu/Debian: sudo apt install python3 python3-pip python3-venv"
        echo "macOS: brew install python3"
        exit 1
    fi
    
    if ! command_exists pip3; then
        print_error "pip3 chưa được cài đặt!"
        echo "Ubuntu/Debian: sudo apt install python3-pip"
        echo "macOS: python3 -m ensurepip --upgrade"
        exit 1
    fi
    
    print_success "Prerequisites OK"
}

# Setup project
setup_project() {
    print_status "Setting up project..."
    
    # Make scripts executable
    chmod +x setup.sh start.sh install_system_deps.sh check_requirements.py
    
    # Run setup if venv doesn't exist
    if [ ! -d "venv" ]; then
        print_status "Chạy setup lần đầu..."
        bash setup.sh
    else
        print_success "Virtual environment đã tồn tại"
    fi
}

# Check MongoDB
ensure_mongodb() {
    print_status "Kiểm tra MongoDB..."
    
    if ! command_exists mongod; then
        print_warning "MongoDB chưa được cài đặt!"
        echo ""
        read -p "Bạn có muốn cài đặt MongoDB? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            if [[ "$OSTYPE" == "linux-gnu"* ]]; then
                print_status "Cài đặt MongoDB trên Linux..."
                bash install_system_deps.sh
            elif [[ "$OSTYPE" == "darwin"* ]]; then
                print_status "Cài đặt MongoDB trên macOS..."
                brew tap mongodb/brew
                brew install mongodb-community
            fi
        else
            print_error "MongoDB là bắt buộc để chạy API!"
            exit 1
        fi
    fi
    
    # Check if MongoDB is running
    if ! nc -z localhost 27017 2>/dev/null; then
        print_status "Khởi động MongoDB..."
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo systemctl start mongod
        elif [[ "$OSTYPE" == "darwin"* ]]; then
            brew services start mongodb/brew/mongodb-community
        fi
        
        # Wait for MongoDB to start
        sleep 3
        
        if ! nc -z localhost 27017 2>/dev/null; then
            print_error "Không thể khởi động MongoDB!"
            exit 1
        fi
    fi
    
    print_success "MongoDB đang chạy"
}

# Run requirements check
check_environment() {
    print_status "Kiểm tra môi trường..."
    
    source venv/bin/activate
    
    if python check_requirements.py; then
        print_success "Môi trường đã sẵn sàng"
    else
        print_error "Môi trường chưa đầy đủ"
        print_status "Thử cài đặt lại packages..."
        pip install -r requirements.txt
        
        # Check again
        if ! python check_requirements.py; then
            print_error "Không thể khắc phục lỗi môi trường"
            exit 1
        fi
    fi
}

# Start API
start_api() {
    print_status "Khởi động Face Recognition API..."
    
    source venv/bin/activate
    
    # Create necessary directories
    mkdir -p public/images logs
    
    echo ""
    print_success "🎉 Face Recognition API đang khởi động..."
    echo ""
    echo "📍 API URL: http://localhost:8000"
    echo "📚 Swagger UI: http://localhost:8000/docs"
    echo "📖 ReDoc: http://localhost:8000/redoc"
    echo "🔍 Health Check: http://localhost:8000/health"
    echo ""
    echo "Nhấn Ctrl+C để dừng API..."
    echo ""
    
    # Start the API
    python run.py
}

# Main execution
main() {
    echo ""
    
    # Check prerequisites
    check_prerequisites
    
    # Setup project
    setup_project
    
    # Ensure MongoDB is running
    ensure_mongodb
    
    # Check environment
    check_environment
    
    # Start API
    start_api
}

# Handle script interruption
trap 'echo -e "\n\n${YELLOW}[INFO]${NC} API đã được dừng. Tạm biệt!"; exit 0' INT

# Run main function
main "$@"