#!/bin/bash

# Script setup Face Recognition API (không sử dụng Docker)
# Usage: bash setup.sh

set -e

echo "🚀 Face Recognition API Setup Script"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python3 is installed
check_python() {
    print_status "Kiểm tra Python3..."
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version 2>&1 | awk '{print $2}')
        print_success "Python3 đã cài đặt: $PYTHON_VERSION"
    else
        print_error "Python3 chưa được cài đặt!"
        echo "Vui lòng cài đặt Python3 trước:"
        echo "Ubuntu/Debian: sudo apt install python3 python3-pip python3-venv"
        echo "macOS: brew install python3"
        exit 1
    fi
}

# Check if pip3 is installed
check_pip() {
    print_status "Kiểm tra pip3..."
    if command -v pip3 &> /dev/null; then
        print_success "pip3 đã cài đặt"
    else
        print_error "pip3 chưa được cài đặt!"
        echo "Ubuntu/Debian: sudo apt install python3-pip"
        echo "macOS: python3 -m ensurepip --upgrade"
        exit 1
    fi
}

# Install system dependencies
install_system_deps() {
    print_status "Cài đặt system dependencies..."

    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        print_status "Phát hiện Linux..."

        # Check if running as root for apt commands
        if [[ $EUID -ne 0 ]]; then
            print_warning "Cần quyền sudo để cài đặt system packages"
            sudo apt-get update
            sudo apt-get install -y \
                build-essential \
                cmake \
                libopenblas-dev \
                liblapack-dev \
                libx11-dev \
                libgtk-3-dev \
                python3-dev \
                python3-pip \
                python3-venv \
                libjpeg-dev \
                libpng-dev \
                libtiff-dev \
                pkg-config \
                libdlib-dev \
                libblas-dev
        else
            apt-get update
            apt-get install -y \
                build-essential \
                cmake \
                libopenblas-dev \
                liblapack-dev \
                libx11-dev \
                libgtk-3-dev \
                python3-dev \
                python3-pip \
                python3-venv \
                libjpeg-dev \
                libpng-dev \
                libtiff-dev \
                pkg-config \
                libdlib-dev \
                libblas-dev
        fi
        print_success "System dependencies đã cài đặt cho Linux"

    elif [[ "$OSTYPE" == "darwin"* ]]; then
        print_status "Phát hiện macOS..."

        if command -v brew &> /dev/null; then
            brew install cmake boost boost-python3 pkg-config
            print_success "System dependencies đã cài đặt cho macOS"
        else
            print_warning "Homebrew chưa cài đặt. Cài đặt Homebrew..."
            /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
            brew install cmake boost boost-python3 pkg-config
        fi
    else
        print_warning "OS không được hỗ trợ tự động cài đặt"
        print_warning "Vui lòng cài đặt cmake và dlib dependencies thủ công"
    fi
}

# Create virtual environment
create_venv() {
    print_status "Tạo môi trường ảo..."

    if [ -d "venv" ]; then
        print_warning "Môi trường ảo đã tồn tại, xóa và tạo lại..."
        rm -rf venv
    fi

    python3 -m venv venv
    print_success "Đã tạo môi trường ảo"
}

# Activate virtual environment and install requirements
install_requirements() {
    print_status "Kích hoạt môi trường ảo và cài đặt packages..."

    source venv/bin/activate

    # Upgrade pip
    pip install --upgrade pip setuptools wheel

    # Install requirements with timeout and retries
    print_status "Cài đặt Python packages (có thể mất vài phút)..."

    # Install numpy first (required for many packages)
    pip install numpy==1.24.3

    # Install face_recognition dependencies separately for better error handling
    pip install cmake
    pip install dlib
    pip install face-recognition==1.3.0

    # Install remaining requirements
    pip install -r requirements.txt

    print_success "Đã cài đặt tất cả Python packages"
}

# Check MongoDB
check_mongodb() {
    print_status "Kiểm tra MongoDB..."

    if command -v mongod &> /dev/null; then
        print_success "MongoDB đã cài đặt"
        print_status "Để khởi động MongoDB, chạy: sudo systemctl start mongod"
    else
        print_warning "MongoDB chưa cài đặt"
        echo ""
        echo "Vui lòng cài đặt MongoDB:"
        echo ""
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            echo "Ubuntu/Debian:"
            echo "  curl -fsSL https://pgp.mongodb.com/server-7.0.asc | sudo gpg -o /usr/share/keyrings/mongodb-server-7.0.gpg --dearmor"
            echo "  echo \"deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-7.0.gpg ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse\" | sudo tee /etc/apt/sources.list.d/mongodb-org-7.0.list"
            echo "  sudo apt-get update"
            echo "  sudo apt-get install -y mongodb-org"
            echo "  sudo systemctl start mongod"
        elif [[ "$OSTYPE" == "darwin"* ]]; then
            echo "macOS:"
            echo "  brew tap mongodb/brew"
            echo "  brew install mongodb-community"
            echo "  brew services start mongodb/brew/mongodb-community"
        fi
        echo ""
    fi
}

# Create directories
create_directories() {
    print_status "Tạo thư mục cần thiết..."
    mkdir -p public/images
    mkdir -p logs
    print_success "Đã tạo thư mục"
}

# Setup environment file
setup_env() {
    print_status "Thiết lập file môi trường..."
    if [ ! -f ".env" ]; then
        cp .env.example .env 2>/dev/null || cat > .env << EOL
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=face_recognition_db
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=true
EOL
        print_success "Đã tạo file .env"
    else
        print_success "File .env đã tồn tại"
    fi
}

# Test installation
test_installation() {
    print_status "Kiểm tra cài đặt..."

    source venv/bin/activate

    python3 -c "
import sys
try:
    import fastapi
    import face_recognition
    import cv2
    import pymongo
    import motor
    print('✅ Tất cả packages chính đã cài đặt thành công')
except ImportError as e:
    print(f'❌ Lỗi import: {e}')
    sys.exit(1)
"

    if [ $? -eq 0 ]; then
        print_success "Cài đặt hoàn tất và hoạt động tốt!"
    else
        print_error "Có lỗi trong quá trình cài đặt"
        exit 1
    fi
}

# Main setup function
main() {
    echo ""
    check_python
    check_pip

    echo ""
    read -p "Cài đặt system dependencies? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        install_system_deps
    fi

    echo ""
    create_venv
    install_requirements
    create_directories
    setup_env
    check_mongodb
    test_installation

    echo ""
    echo "🎉 Setup hoàn tất!"
    echo ""
    echo "Để chạy ứng dụng:"
    echo "1. Kích hoạt môi trường ảo:"
    echo "   source venv/bin/activate"
    echo ""
    echo "2. Đả
m bảo MongoDB đang chạy:"
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "   sudo systemctl start mongod"
        echo "   sudo systemctl status mongod"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "   brew services start mongodb/brew/mongodb-community"
        echo
 "   brew services list | grep mongo"
    fi
    echo ""
    echo "3. Chạy API:"
    echo "   python run.py"
    echo ""
    echo "4. Truy cập Swagger UI:"
    echo "   http://localhost:8000/docs"
    echo ""
    echo "5. Test API:"
    echo "   python test_api.py --register-image path/to/photo.jpg"
    echo ""
    
    # Ask if user wants to start MongoDB and run the app
    echo ""
    read -p "Bạn có muốn khởi động MongoDB và chạy API ngay bây giờ? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        start_services
    fi
}

# Start services
start_services() {
    print_status "Khởi động services..."
    
    # Start MongoDB
    if command -v mongod &> /dev/null; then
        print_status "Khởi động MongoDB..."
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo systemctl start mongod
            print_status "Kiểm tra trạng thái MongoDB..."
            sudo systemctl status mongod --no-pager -l
        elif [[ "$OSTYPE" == "darwin"* ]]; then
            brew services start mongodb/brew/mongodb-community
            print_status "MongoDB đã được khởi động"
        fi
        
        # Wait a bit for MongoDB to start
        sleep 3
    else
        print_warning "MongoDB chưa được cài đặt, vui lòng cài đặt trước"
        return 1
    fi
    
    # Activate venv and run API
    print_status "Khởi động Face Recognition API..."
    source venv/bin/activate
    python run.py
}

# Run main function
main "$@"