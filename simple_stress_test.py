#!/usr/bin/env python3
"""
Simple Stress Test cho API Face Recognition Verify
Sử dụng requests và threading - dễ hiểu và setup
"""

import requests
import threading
import time
import json
import os
from datetime import datetime
from typing import List, Dict, Any
import statistics

class SimpleStressTest:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.verify_endpoint = f"{base_url}/face-recognition/verify"
        self.results = []
        self.lock = threading.Lock()
        
    def send_verify_request(self, image_path: str, threshold: float = 0.5, 
                          save_result_image: bool = False, user_id: int = 0, 
                          request_num: int = 0) -> Dict[str, Any]:
        """Gửi một request verify"""
        start_time = time.time()
        
        try:
            # Chuẩn bị data
            data = {
                'threshold': threshold,
                'save_result_image': save_result_image
            }
            
            # Chuẩn bị file
            with open(image_path, 'rb') as f:
                files = {'image': (os.path.basename(image_path), f, 'image/jpeg')}
                
                response = requests.post(
                    self.verify_endpoint, 
                    data=data, 
                    files=files,
                    timeout=60
                )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            result = {
                'user_id': user_id,
                'request_number': request_num,
                'status_code': response.status_code,
                'response_time': response_time,
                'success': response.status_code == 200,
                'timestamp': datetime.now().isoformat(),
                'image_path': image_path
            }
            
            if response.status_code == 200:
                response_data = response.json()
                result['total_faces_detected'] = response_data.get('total_faces_detected', 0)
                result['matches_count'] = len(response_data.get('matches', []))
            else:
                result['error'] = response.text[:200]  # Limit error message
            
            return result
            
        except Exception as e:
            end_time = time.time()
            return {
                'user_id': user_id,
                'request_number': request_num,
                'status_code': 0,
                'response_time': end_time - start_time,
                'success': False,
                'error': str(e)[:200],
                'timestamp': datetime.now().isoformat(),
                'image_path': image_path
            }

    def user_worker(self, user_id: int, image_paths: List[str], 
                   requests_per_user: int, threshold: float):
        """Worker function cho mỗi user thread"""
        print(f"🚀 User {user_id} bắt đầu...")
        
        for i in range(requests_per_user):
            # Chọn ảnh ngẫu nhiên
            image_path = image_paths[i % len(image_paths)]
            
            print(f"User {user_id} - Request {i+1}/{requests_per_user}")
            
            result = self.send_verify_request(
                image_path, threshold, False, user_id, i+1
            )
            
            # Thread-safe add to results
            with self.lock:
                self.results.append(result)
            
            # Delay nhỏ giữa requests
            time.sleep(0.1)
        
        print(f"✅ User {user_id} hoàn thành!")

    def run_stress_test(self, image_paths: List[str], 
                       concurrent_users: int = 10,
                       requests_per_user: int = 5,
                       threshold: float = 0.5) -> List[Dict[str, Any]]:
        """Chạy stress test với threading"""
        
        self.results = []  # Reset results
        
        print(f"🔥 Bắt đầu stress test:")
        print(f"   - {concurrent_users} users đồng thời")
        print(f"   - {requests_per_user} requests/user")
        print(f"   - Tổng: {concurrent_users * requests_per_user} requests")
        print(f"   - Threshold: {threshold}")
        
        start_time = time.time()
        
        # Tạo và start threads
        threads = []
        for user_id in range(concurrent_users):
            thread = threading.Thread(
                target=self.user_worker,
                args=(user_id, image_paths, requests_per_user, threshold)
            )
            threads.append(thread)
            thread.start()
        
        # Đợi tất cả threads hoàn thành
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"⏱️  Hoàn thành trong {total_time:.2f} giây")
        
        return self.results.copy()

    def analyze_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Phân tích kết quả"""
        if not results:
            return {"error": "Không có kết quả"}
        
        total_requests = len(results)
        successful_requests = len([r for r in results if r['success']])
        failed_requests = total_requests - successful_requests
        success_rate = (successful_requests / total_requests) * 100
        
        # Response times của requests thành công
        response_times = [r['response_time'] for r in results if r['success']]
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            median_response_time = statistics.median(response_times)
        else:
            avg_response_time = min_response_time = max_response_time = median_response_time = 0
        
        # Tính throughput
        if results:
            timestamps = [datetime.fromisoformat(r['timestamp']) for r in results]
            total_duration = (max(timestamps) - min(timestamps)).total_seconds()
            throughput = successful_requests / total_duration if total_duration > 0 else 0
        else:
            throughput = 0
        
        # Phân tích lỗi
        error_analysis = {}
        for result in results:
            if not result['success']:
                status = result.get('status_code', 'Unknown')
                error_analysis[f"HTTP_{status}"] = error_analysis.get(f"HTTP_{status}", 0) + 1
        
        return {
            "summary": {
                "total_requests": total_requests,
                "successful_requests": successful_requests,
                "failed_requests": failed_requests,
                "success_rate_percent": round(success_rate, 2),
                "throughput_rps": round(throughput, 2)
            },
            "performance": {
                "avg_response_time": round(avg_response_time, 3),
                "min_response_time": round(min_response_time, 3),
                "max_response_time": round(max_response_time, 3),
                "median_response_time": round(median_response_time, 3)
            },
            "errors": error_analysis
        }

    def print_results(self, analysis: Dict[str, Any]):
        """In kết quả đẹp"""
        print("\n" + "="*60)
        print("📊 KẾT QUẢ STRESS TEST")
        print("="*60)
        
        summary = analysis['summary']
        print(f"📈 Tổng quan:")
        print(f"   Tổng requests: {summary['total_requests']}")
        print(f"   Thành công: {summary['successful_requests']}")
        print(f"   Thất bại: {summary['failed_requests']}")
        print(f"   Tỷ lệ thành công: {summary['success_rate_percent']}%")
        print(f"   Throughput: {summary['throughput_rps']} req/s")
        
        perf = analysis['performance']
        print(f"\n⚡ Hiệu suất:")
        print(f"   Thời gian phản hồi trung bình: {perf['avg_response_time']}s")
        print(f"   Nhanh nhất: {perf['min_response_time']}s")
        print(f"   Chậm nhất: {perf['max_response_time']}s")
        print(f"   Median: {perf['median_response_time']}s")
        
        if analysis['errors']:
            print(f"\n❌ Lỗi:")
            for error, count in analysis['errors'].items():
                print(f"   {error}: {count} lần")
        
        print("="*60)

    def save_results(self, results: List[Dict[str, Any]], analysis: Dict[str, Any]):
        """Lưu kết quả"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"stress_test_simple_{timestamp}.json"
        
        data = {
            "test_info": {
                "timestamp": datetime.now().isoformat(),
                "endpoint": self.verify_endpoint,
                "tool": "simple_stress_test"
            },
            "analysis": analysis,
            "results": results
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Kết quả đã lưu: {filename}")

def main():
    """Main function"""
    
    # ⚙️ CẤU HÌNH TEST
    BASE_URL = "http://localhost:8000"
    CONCURRENT_USERS = 5      # Số user đồng thời
    REQUESTS_PER_USER = 3     # Số request mỗi user  
    THRESHOLD = 0.5           # Ngưỡng nhận diện
    
    # 📁 ĐƯỜNG DẪN ẢNH TEST
    IMAGE_PATHS = [
        "public/images/test1.jpg",
        "public/images/test2.jpg",
        # Thêm ảnh test của bạn ở đây
    ]
    
    # Kiểm tra ảnh
    existing_images = [path for path in IMAGE_PATHS if os.path.exists(path)]
    
    if not existing_images:
        print("❌ Không tìm thấy ảnh test!")
        print("📝 Hướng dẫn:")
        print("   1. Thêm ảnh vào thư mục public/images/")
        print("   2. Hoặc cập nhật IMAGE_PATHS trong script")
        print("   3. Đảm bảo server đang chạy tại", BASE_URL)
        return
    
    print(f"✅ Tìm thấy {len(existing_images)} ảnh test")
    
    # Kiểm tra server
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ Server đang chạy")
        else:
            print("⚠️  Server có vấn đề")
    except:
        print("❌ Không thể kết nối server!")
        print(f"   Đảm bảo server chạy tại {BASE_URL}")
        return
    
    # Chạy test
    tester = SimpleStressTest(BASE_URL)
    
    try:
        results = tester.run_stress_test(
            existing_images,
            CONCURRENT_USERS,
            REQUESTS_PER_USER,
            THRESHOLD
        )
        
        analysis = tester.analyze_results(results)
        tester.print_results(analysis)
        tester.save_results(results, analysis)
        
    except KeyboardInterrupt:
        print("\n⏹️  Test bị dừng bởi user")
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
