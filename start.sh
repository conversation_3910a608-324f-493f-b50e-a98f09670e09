#!/bin/bash

# Script khởi động Face Recognition API (không sử dụng Docker)
# Usage: bash start.sh

echo "🚀 Khởi động Face Recognition API..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Môi trường ảo chưa tồn tại!"
    echo "Chạy setup script trước: bash setup.sh"
    exit 1
fi

# Activate virtual environment
echo "📦 Kích hoạt môi trường ảo..."
source venv/bin/activate

# Check if MongoDB is running
echo "🗄️ Kiểm tra MongoDB..."
if ! nc -z localhost 27017 2>/dev/null; then
    echo "⚠️ MongoDB chưa chạy!"
    
    # Try to start MongoDB based on OS
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if command -v systemctl &> /dev/null; then
            echo "🐧 Khởi động MongoDB trên Linux..."
            sudo systemctl start mongod
            sleep 3
            
            if nc -z localhost 27017 2>/dev/null; then
                echo "✅ MongoDB đã sẵn sàng!"
            else
                echo "❌ Không thể khởi động MongoDB!"
                echo "Vui lòng khởi động MongoDB thủ công:"
                echo "  sudo systemctl start mongod"
                exit 1
            fi
        else
            echo "❌ systemctl không có sẵn!"
            echo "Vui lòng khởi động MongoDB thủ công"
            exit 1
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        if command -v brew &> /dev/null; then
            echo "🍎 Khởi động MongoDB trên macOS..."
            brew services start mongodb/brew/mongodb-community
            sleep 3
            
            if nc -z localhost 27017 2>/dev/null; then
                echo "✅ MongoDB đã sẵn sàng!"
            else
                echo "❌ Không thể khởi động MongoDB!"
                echo "Vui lòng khởi động MongoDB thủ công:"
                echo "  brew services start mongodb/brew/mongodb-community"
                exit 1
            fi
        else
            echo "❌ Homebrew không có sẵn!"
            echo "Vui lòng khởi động MongoDB thủ công"
            exit 1
        fi
    else
        echo "❌ OS không được hỗ trợ tự động khởi động MongoDB!"
        echo "Vui lòng khởi động MongoDB thủ công"
        exit 1
    fi
else
    echo "✅ MongoDB đang chạy!"
fi

# Create directories if not exist
mkdir -p public/images
mkdir -p logs

# Set environment variables
export MONGODB_URL=${MONGODB_URL:-"mongodb://localhost:27017"}
export DATABASE_NAME=${DATABASE_NAME:-"face_recognition_db"}

echo "🌐 Khởi động Face Recognition API..."
echo "📍 API sẽ chạy tại: http://localhost:8000"
echo "📚 Swagger UI: http://localhost:8000/docs"
echo "📖 ReDoc: http://localhost:8000/redoc"
echo ""
echo "Nhấn Ctrl+C để dừng..."
echo ""

# Start the API
python run.py