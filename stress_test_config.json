{"stress_test_config": {"base_url": "http://localhost:8000", "concurrent_users": [1, 5, 10, 20], "requests_per_user": 5, "threshold": 0.5, "test_scenarios": [{"name": "Light Load", "concurrent_users": 1, "requests_per_user": 10}, {"name": "Medium Load", "concurrent_users": 5, "requests_per_user": 5}, {"name": "Heavy Load", "concurrent_users": 10, "requests_per_user": 3}, {"name": "Extreme Load", "concurrent_users": 20, "requests_per_user": 2}]}}