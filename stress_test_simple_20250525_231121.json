{"test_info": {"timestamp": "2025-05-25T23:11:21.965168", "endpoint": "http://localhost:8000/face-recognition/verify", "tool": "simple_stress_test"}, "analysis": {"summary": {"total_requests": 15, "successful_requests": 0, "failed_requests": 15, "success_rate_percent": 0.0, "throughput_rps": 0.0}, "performance": {"avg_response_time": 0, "min_response_time": 0, "max_response_time": 0, "median_response_time": 0}, "errors": {"HTTP_404": 15}}, "results": [{"user_id": 1, "request_number": 1, "status_code": 404, "response_time": 0.004543781280517578, "success": false, "timestamp": "2025-05-25T23:11:21.637636", "image_path": "public/images/test/test_image_001.jpg", "error": "{\"detail\":\"Not Found\"}"}, {"user_id": 2, "request_number": 1, "status_code": 404, "response_time": 0.004525899887084961, "success": false, "timestamp": "2025-05-25T23:11:21.637813", "image_path": "public/images/test/test_image_001.jpg", "error": "{\"detail\":\"Not Found\"}"}, {"user_id": 0, "request_number": 1, "status_code": 404, "response_time": 0.005154132843017578, "success": false, "timestamp": "2025-05-25T23:11:21.638010", "image_path": "public/images/test/test_image_001.jpg", "error": "{\"detail\":\"Not Found\"}"}, {"user_id": 3, "request_number": 1, "status_code": 404, "response_time": 0.004338979721069336, "success": false, "timestamp": "2025-05-25T23:11:21.638222", "image_path": "public/images/test/test_image_001.jpg", "error": "{\"detail\":\"Not Found\"}"}, {"user_id": 4, "request_number": 1, "status_code": 404, "response_time": 0.004300594329833984, "success": false, "timestamp": "2025-05-25T23:11:21.638376", "image_path": "public/images/test/test_image_001.jpg", "error": "{\"detail\":\"Not Found\"}"}, {"user_id": 2, "request_number": 2, "status_code": 404, "response_time": 0.006326913833618164, "success": false, "timestamp": "2025-05-25T23:11:21.747301", "image_path": "public/images/test/test_image_002.jpg", "error": "{\"detail\":\"Not Found\"}"}, {"user_id": 0, "request_number": 2, "status_code": 404, "response_time": 0.006044149398803711, "success": false, "timestamp": "2025-05-25T23:11:21.747079", "image_path": "public/images/test/test_image_002.jpg", "error": "{\"detail\":\"Not Found\"}"}, {"user_id": 4, "request_number": 2, "status_code": 404, "response_time": 0.0058498382568359375, "success": false, "timestamp": "2025-05-25T23:11:21.747508", "image_path": "public/images/test/test_image_002.jpg", "error": "{\"detail\":\"Not Found\"}"}, {"user_id": 3, "request_number": 2, "status_code": 404, "response_time": 0.006165027618408203, "success": false, "timestamp": "2025-05-25T23:11:21.747675", "image_path": "public/images/test/test_image_002.jpg", "error": "{\"detail\":\"Not Found\"}"}, {"user_id": 1, "request_number": 2, "status_code": 404, "response_time": 0.0062868595123291016, "success": false, "timestamp": "2025-05-25T23:11:21.747839", "image_path": "public/images/test/test_image_002.jpg", "error": "{\"detail\":\"Not Found\"}"}, {"user_id": 3, "request_number": 3, "status_code": 404, "response_time": 0.007986068725585938, "success": false, "timestamp": "2025-05-25T23:11:21.858778", "image_path": "public/images/test/test_image_003.jpg", "error": "{\"detail\":\"Not Found\"}"}, {"user_id": 0, "request_number": 3, "status_code": 404, "response_time": 0.008355855941772461, "success": false, "timestamp": "2025-05-25T23:11:21.859070", "image_path": "public/images/test/test_image_003.jpg", "error": "{\"detail\":\"Not Found\"}"}, {"user_id": 1, "request_number": 3, "status_code": 404, "response_time": 0.008630037307739258, "success": false, "timestamp": "2025-05-25T23:11:21.859556", "image_path": "public/images/test/test_image_003.jpg", "error": "{\"detail\":\"Not Found\"}"}, {"user_id": 2, "request_number": 3, "status_code": 404, "response_time": 0.008521795272827148, "success": false, "timestamp": "2025-05-25T23:11:21.859278", "image_path": "public/images/test/test_image_003.jpg", "error": "{\"detail\":\"Not Found\"}"}, {"user_id": 4, "request_number": 3, "status_code": 404, "response_time": 0.00894308090209961, "success": false, "timestamp": "2025-05-25T23:11:21.859757", "image_path": "public/images/test/test_image_003.jpg", "error": "{\"detail\":\"Not Found\"}"}]}