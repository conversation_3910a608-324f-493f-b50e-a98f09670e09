{"test_info": {"timestamp": "2025-05-25T23:12:35.757773", "endpoint": "http://localhost:8000/api/face-recognition/verify", "tool": "simple_stress_test"}, "analysis": {"summary": {"total_requests": 15, "successful_requests": 15, "failed_requests": 0, "success_rate_percent": 100.0, "throughput_rps": 7.31}, "performance": {"avg_response_time": 0.992, "min_response_time": 0.797, "max_response_time": 1.174, "median_response_time": 0.993}, "errors": {}}, "results": [{"user_id": 1, "request_number": 1, "status_code": 200, "response_time": 1.1738648414611816, "success": true, "timestamp": "2025-05-25T23:12:33.601395", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 3, "request_number": 1, "status_code": 200, "response_time": 1.173766851425171, "success": true, "timestamp": "2025-05-25T23:12:33.601595", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 4, "request_number": 1, "status_code": 200, "response_time": 1.1737210750579834, "success": true, "timestamp": "2025-05-25T23:12:33.601919", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 0, "request_number": 1, "status_code": 200, "response_time": 1.1742579936981201, "success": true, "timestamp": "2025-05-25T23:12:33.601759", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 2, "request_number": 1, "status_code": 200, "response_time": 1.174468755722046, "success": true, "timestamp": "2025-05-25T23:12:33.602065", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 0, "request_number": 2, "status_code": 200, "response_time": 0.8472170829772949, "success": true, "timestamp": "2025-05-25T23:12:34.554627", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 2, "request_number": 2, "status_code": 200, "response_time": 0.8462121486663818, "success": true, "timestamp": "2025-05-25T23:12:34.554466", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 3, "request_number": 2, "status_code": 200, "response_time": 0.8479509353637695, "success": true, "timestamp": "2025-05-25T23:12:34.554792", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 1, "request_number": 2, "status_code": 200, "response_time": 0.8481278419494629, "success": true, "timestamp": "2025-05-25T23:12:34.554911", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 4, "request_number": 2, "status_code": 200, "response_time": 0.8481359481811523, "success": true, "timestamp": "2025-05-25T23:12:34.555027", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 0, "request_number": 3, "status_code": 200, "response_time": 0.7973887920379639, "success": true, "timestamp": "2025-05-25T23:12:35.455826", "image_path": "public/images/test/test_image_003.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 2, "request_number": 3, "status_code": 200, "response_time": 0.992919921875, "success": true, "timestamp": "2025-05-25T23:12:35.651668", "image_path": "public/images/test/test_image_003.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 1, "request_number": 3, "status_code": 200, "response_time": 0.9933881759643555, "success": true, "timestamp": "2025-05-25T23:12:35.651866", "image_path": "public/images/test/test_image_003.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 4, "request_number": 3, "status_code": 200, "response_time": 0.9931759834289551, "success": true, "timestamp": "2025-05-25T23:12:35.652032", "image_path": "public/images/test/test_image_003.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 3, "request_number": 3, "status_code": 200, "response_time": 0.9937660694122314, "success": true, "timestamp": "2025-05-25T23:12:35.652224", "image_path": "public/images/test/test_image_003.jpg", "total_faces_detected": 1, "matches_count": 1}]}