{"test_info": {"timestamp": "2025-05-25T23:13:39.531949", "endpoint": "http://localhost:8000/api/face-recognition/verify", "tool": "simple_stress_test"}, "analysis": {"summary": {"total_requests": 30, "successful_requests": 30, "failed_requests": 0, "success_rate_percent": 100.0, "throughput_rps": 5.54}, "performance": {"avg_response_time": 2.541, "min_response_time": 0.486, "max_response_time": 3.471, "median_response_time": 2.512}, "errors": {}}, "results": [{"user_id": 1, "request_number": 1, "status_code": 200, "response_time": 0.48584675788879395, "success": true, "timestamp": "2025-05-25T23:13:34.014466", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 0, "request_number": 1, "status_code": 200, "response_time": 1.2082231044769287, "success": true, "timestamp": "2025-05-25T23:13:34.736796", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 2, "request_number": 1, "status_code": 200, "response_time": 1.4384839534759521, "success": true, "timestamp": "2025-05-25T23:13:34.967420", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 3, "request_number": 1, "status_code": 200, "response_time": 1.4379618167877197, "success": true, "timestamp": "2025-05-25T23:13:34.967606", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 6, "request_number": 1, "status_code": 200, "response_time": 2.3407061100006104, "success": true, "timestamp": "2025-05-25T23:13:35.872025", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 5, "request_number": 1, "status_code": 200, "response_time": 2.3416669368743896, "success": true, "timestamp": "2025-05-25T23:13:35.872243", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 7, "request_number": 1, "status_code": 200, "response_time": 2.7933599948883057, "success": true, "timestamp": "2025-05-25T23:13:36.326212", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 8, "request_number": 1, "status_code": 200, "response_time": 2.7936031818389893, "success": true, "timestamp": "2025-05-25T23:13:36.326421", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 9, "request_number": 1, "status_code": 200, "response_time": 2.7930870056152344, "success": true, "timestamp": "2025-05-25T23:13:36.326589", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 4, "request_number": 1, "status_code": 200, "response_time": 2.7960991859436035, "success": true, "timestamp": "2025-05-25T23:13:36.326755", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 10, "request_number": 1, "status_code": 200, "response_time": 3.2457220554351807, "success": true, "timestamp": "2025-05-25T23:13:36.779351", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 11, "request_number": 1, "status_code": 200, "response_time": 3.2453601360321045, "success": true, "timestamp": "2025-05-25T23:13:36.779543", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 14, "request_number": 1, "status_code": 200, "response_time": 3.468630075454712, "success": true, "timestamp": "2025-05-25T23:13:37.007568", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 12, "request_number": 1, "status_code": 200, "response_time": 3.4712607860565186, "success": true, "timestamp": "2025-05-25T23:13:37.007764", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 13, "request_number": 1, "status_code": 200, "response_time": 3.47062087059021, "success": true, "timestamp": "2025-05-25T23:13:37.008575", "image_path": "public/images/test/test_image_001.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 1, "request_number": 2, "status_code": 200, "response_time": 3.381338119506836, "success": true, "timestamp": "2025-05-25T23:13:37.496268", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 0, "request_number": 2, "status_code": 200, "response_time": 3.135962963104248, "success": true, "timestamp": "2025-05-25T23:13:37.977856", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 2, "request_number": 2, "status_code": 200, "response_time": 3.3867580890655518, "success": true, "timestamp": "2025-05-25T23:13:38.459460", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 6, "request_number": 2, "status_code": 200, "response_time": 2.486575126647949, "success": true, "timestamp": "2025-05-25T23:13:38.459875", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 3, "request_number": 2, "status_code": 200, "response_time": 3.386997938156128, "success": true, "timestamp": "2025-05-25T23:13:38.459713", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 8, "request_number": 2, "status_code": 200, "response_time": 2.1897618770599365, "success": true, "timestamp": "2025-05-25T23:13:38.621080", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 4, "request_number": 2, "status_code": 200, "response_time": 2.1890649795532227, "success": true, "timestamp": "2025-05-25T23:13:38.620893", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 5, "request_number": 2, "status_code": 200, "response_time": 2.6478967666625977, "success": true, "timestamp": "2025-05-25T23:13:38.621225", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 9, "request_number": 2, "status_code": 200, "response_time": 2.511296033859253, "success": true, "timestamp": "2025-05-25T23:13:38.943154", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 7, "request_number": 2, "status_code": 200, "response_time": 2.51200795173645, "success": true, "timestamp": "2025-05-25T23:13:38.943339", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 11, "request_number": 2, "status_code": 200, "response_time": 2.0590639114379883, "success": true, "timestamp": "2025-05-25T23:13:38.943532", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 10, "request_number": 2, "status_code": 200, "response_time": 2.059221029281616, "success": true, "timestamp": "2025-05-25T23:13:38.943707", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 13, "request_number": 2, "status_code": 200, "response_time": 2.3142690658569336, "success": true, "timestamp": "2025-05-25T23:13:39.427188", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 14, "request_number": 2, "status_code": 200, "response_time": 2.314516067504883, "success": true, "timestamp": "2025-05-25T23:13:39.427371", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}, {"user_id": 12, "request_number": 2, "status_code": 200, "response_time": 2.3147120475769043, "success": true, "timestamp": "2025-05-25T23:13:39.427582", "image_path": "public/images/test/test_image_002.jpg", "total_faces_detected": 1, "matches_count": 1}]}