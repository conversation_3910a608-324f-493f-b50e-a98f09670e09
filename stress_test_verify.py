#!/usr/bin/env python3
"""
Stress Test cho API Face Recognition Verify
Sử dụng asyncio để test hi<PERSON><PERSON> <PERSON><PERSON><PERSON> v<PERSON><PERSON> <PERSON><PERSON> request đồng thời
"""

import asyncio
import aiohttp
import time
import json
import os
from typing import List, Dict, Any
import statistics
from datetime import datetime

class StressTestVerify:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.verify_endpoint = f"{base_url}/api/face-recognition/verify"
        self.results = []

    async def send_verify_request(self, session: aiohttp.ClientSession,
                                image_path: str, threshold: float = 0.5,
                                save_result_image: bool = False) -> Dict[str, Any]:
        """Gửi một request verify"""
        start_time = time.time()

        try:
            # Chuẩn bị form data
            data = aiohttp.FormData()
            data.add_field('threshold', str(threshold))
            data.add_field('save_result_image', str(save_result_image).lower())

            # Thêm file ảnh
            with open(image_path, 'rb') as f:
                data.add_field('image', f, filename=os.path.basename(image_path))

                async with session.post(self.verify_endpoint, data=data) as response:
                    end_time = time.time()
                    response_time = end_time - start_time

                    result = {
                        'status_code': response.status,
                        'response_time': response_time,
                        'success': response.status == 200,
                        'timestamp': datetime.now().isoformat(),
                        'image_path': image_path
                    }

                    if response.status == 200:
                        response_data = await response.json()
                        result['total_faces_detected'] = response_data.get('total_faces_detected', 0)
                        result['matches_count'] = len(response_data.get('matches', []))
                    else:
                        result['error'] = await response.text()

                    return result

        except Exception as e:
            end_time = time.time()
            return {
                'status_code': 0,
                'response_time': end_time - start_time,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'image_path': image_path
            }

    async def run_concurrent_requests(self, image_paths: List[str],
                                    concurrent_users: int = 10,
                                    requests_per_user: int = 5,
                                    threshold: float = 0.5) -> List[Dict[str, Any]]:
        """Chạy nhiều request đồng thời"""

        async def user_session(user_id: int):
            """Mô phỏng một user gửi nhiều request"""
            connector = aiohttp.TCPConnector(limit=100)
            timeout = aiohttp.ClientTimeout(total=60)

            async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
                user_results = []

                for i in range(requests_per_user):
                    # Chọn ảnh ngẫu nhiên
                    image_path = image_paths[i % len(image_paths)]

                    print(f"User {user_id} - Request {i+1}/{requests_per_user}")

                    result = await self.send_verify_request(
                        session, image_path, threshold, save_result_image=False
                    )
                    result['user_id'] = user_id
                    result['request_number'] = i + 1
                    user_results.append(result)

                    # Delay nhỏ giữa các request của cùng user
                    await asyncio.sleep(0.1)

                return user_results

        # Tạo tasks cho tất cả users
        tasks = [user_session(user_id) for user_id in range(concurrent_users)]

        print(f"Bắt đầu stress test với {concurrent_users} users đồng thời...")
        print(f"Mỗi user sẽ gửi {requests_per_user} requests")
        print(f"Tổng cộng: {concurrent_users * requests_per_user} requests")

        start_time = time.time()

        # Chạy tất cả tasks đồng thời
        all_results = await asyncio.gather(*tasks)

        end_time = time.time()
        total_time = end_time - start_time

        # Flatten results
        results = []
        for user_results in all_results:
            results.extend(user_results)

        print(f"Hoàn thành stress test trong {total_time:.2f} giây")

        return results

    def analyze_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Phân tích kết quả stress test"""

        if not results:
            return {"error": "Không có kết quả để phân tích"}

        # Tính toán các metrics
        total_requests = len(results)
        successful_requests = len([r for r in results if r['success']])
        failed_requests = total_requests - successful_requests
        success_rate = (successful_requests / total_requests) * 100

        # Response times
        response_times = [r['response_time'] for r in results if r['success']]

        if response_times:
            avg_response_time = statistics.mean(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            median_response_time = statistics.median(response_times)

            # Percentiles
            response_times_sorted = sorted(response_times)
            p95_index = int(0.95 * len(response_times_sorted))
            p99_index = int(0.99 * len(response_times_sorted))
            p95_response_time = response_times_sorted[p95_index] if p95_index < len(response_times_sorted) else max_response_time
            p99_response_time = response_times_sorted[p99_index] if p99_index < len(response_times_sorted) else max_response_time
        else:
            avg_response_time = min_response_time = max_response_time = median_response_time = 0
            p95_response_time = p99_response_time = 0

        # Throughput
        total_time = max([datetime.fromisoformat(r['timestamp']) for r in results]) - \
                    min([datetime.fromisoformat(r['timestamp']) for r in results])
        throughput = successful_requests / total_time.total_seconds() if total_time.total_seconds() > 0 else 0

        # Error analysis
        error_types = {}
        for result in results:
            if not result['success']:
                error_key = f"HTTP_{result['status_code']}"
                if 'error' in result:
                    error_key += f": {result['error'][:50]}"
                error_types[error_key] = error_types.get(error_key, 0) + 1

        analysis = {
            "summary": {
                "total_requests": total_requests,
                "successful_requests": successful_requests,
                "failed_requests": failed_requests,
                "success_rate_percent": round(success_rate, 2),
                "throughput_rps": round(throughput, 2)
            },
            "response_times": {
                "average_seconds": round(avg_response_time, 3),
                "minimum_seconds": round(min_response_time, 3),
                "maximum_seconds": round(max_response_time, 3),
                "median_seconds": round(median_response_time, 3),
                "p95_seconds": round(p95_response_time, 3),
                "p99_seconds": round(p99_response_time, 3)
            },
            "errors": error_types
        }

        return analysis

    def save_results(self, results: List[Dict[str, Any]], analysis: Dict[str, Any],
                    filename: str = None):
        """Lưu kết quả vào file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"stress_test_results_{timestamp}.json"

        data = {
            "test_info": {
                "timestamp": datetime.now().isoformat(),
                "endpoint": self.verify_endpoint,
                "total_requests": len(results)
            },
            "analysis": analysis,
            "detailed_results": results
        }

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        print(f"Kết quả đã được lưu vào: {filename}")

async def main():
    """Hàm main để chạy stress test"""

    # Cấu hình test
    BASE_URL = "http://localhost:8000"
    CONCURRENT_USERS = 10  # Số user đồng thời
    REQUESTS_PER_USER = 5  # Số request mỗi user
    THRESHOLD = 0.5

    # Danh sách ảnh test (thay đổi đường dẫn theo ảnh của bạn)
    IMAGE_PATHS = [
        "public/images/test/test_image_001.jpg",
        "public/images/test/test_image_002.jpg",
        "public/images/test/test_image_003.jpg",
        "public/images/test/test_image_004.jpg",
        "public/images/test/test_image_005.jpg",
    ]

    # Kiểm tra ảnh tồn tại
    existing_images = [path for path in IMAGE_PATHS if os.path.exists(path)]

    if not existing_images:
        print("❌ Không tìm thấy ảnh test nào!")
        print("Vui lòng thêm ảnh vào thư mục public/images/ hoặc cập nhật IMAGE_PATHS")
        return

    print(f"✅ Tìm thấy {len(existing_images)} ảnh test")

    # Tạo stress tester
    tester = StressTestVerify(BASE_URL)

    try:
        # Chạy stress test
        results = await tester.run_concurrent_requests(
            existing_images,
            CONCURRENT_USERS,
            REQUESTS_PER_USER,
            THRESHOLD
        )

        # Phân tích kết quả
        analysis = tester.analyze_results(results)

        # In kết quả
        print("\n" + "="*50)
        print("📊 KẾT QUẢ STRESS TEST")
        print("="*50)

        summary = analysis['summary']
        print(f"Tổng requests: {summary['total_requests']}")
        print(f"Thành công: {summary['successful_requests']}")
        print(f"Thất bại: {summary['failed_requests']}")
        print(f"Tỷ lệ thành công: {summary['success_rate_percent']}%")
        print(f"Throughput: {summary['throughput_rps']} requests/second")

        response_times = analysis['response_times']
        print(f"\nThời gian phản hồi:")
        print(f"  Trung bình: {response_times['average_seconds']}s")
        print(f"  Tối thiểu: {response_times['minimum_seconds']}s")
        print(f"  Tối đa: {response_times['maximum_seconds']}s")
        print(f"  Median: {response_times['median_seconds']}s")
        print(f"  P95: {response_times['p95_seconds']}s")
        print(f"  P99: {response_times['p99_seconds']}s")

        if analysis['errors']:
            print(f"\nLỗi:")
            for error, count in analysis['errors'].items():
                print(f"  {error}: {count} lần")

        # Lưu kết quả
        tester.save_results(results, analysis)

    except Exception as e:
        print(f"❌ Lỗi khi chạy stress test: {e}")

if __name__ == "__main__":
    asyncio.run(main())
