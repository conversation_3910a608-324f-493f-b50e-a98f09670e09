import requests
import json
import os
from typing import Dict, Any

class FaceRecognitionAPITester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def test_health_check(self) -> Dict[str, Any]:
        """Test health check endpoints"""
        print("🏥 Testing health check...")
        
        # Test root endpoint
        response = self.session.get(f"{self.base_url}/")
        print(f"GET / - Status: {response.status_code}")
        if response.status_code == 200:
            print(f"Response: {response.json()}")
        
        # Test health endpoint
        response = self.session.get(f"{self.base_url}/health")
        print(f"GET /health - Status: {response.status_code}")
        if response.status_code == 200:
            print(f"Response: {response.json()}")
        
        return response.json() if response.status_code == 200 else {}
    
    def test_register_person(self, image_path: str) -> Dict[str, Any]:
        """Test register person API"""
        print("👤 Testing person registration...")
        
        if not os.path.exists(image_path):
            print(f"❌ Image file not found: {image_path}")
            return {}
        
        url = f"{self.base_url}/api/persons/register"
        
        data = {
            "name": "Nguyễn Văn Test",
            "email": "<EMAIL>",
            "phone": "**********",
            "description": "Test user for API testing"
        }
        
        files = {"image": open(image_path, "rb")}
        
        try:
            response = self.session.post(url, data=data, files=files)
            print(f"POST /api/persons/register - Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Person registered successfully!")
                print(f"ID: {result.get('id')}")
                print(f"Name: {result.get('name')}")
                return result
            else:
                print(f"❌ Registration failed: {response.text}")
                return {}
                
        except Exception as e:
            print(f"❌ Error during registration: {e}")
            return {}
        finally:
            files["image"].close()
    
    def test_get_persons(self) -> Dict[str, Any]:
        """Test get persons list"""
        print("📋 Testing get persons...")
        
        url = f"{self.base_url}/api/persons/"
        params = {"skip": 0, "limit": 10}
        
        response = self.session.get(url, params=params)
        print(f"GET /api/persons/ - Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Found {len(result)} persons")
            for person in result:
                print(f"  - {person.get('name')} ({person.get('id')})")
            return result
        else:
            print(f"❌ Failed to get persons: {response.text}")
            return {}
    
    def test_face_verification(self, image_path: str) -> Dict[str, Any]:
        """Test face verification API"""
        print("🔍 Testing face verification...")
        
        if not os.path.exists(image_path):
            print(f"❌ Image file not found: {image_path}")
            return {}
        
        url = f"{self.base_url}/api/face-recognition/verify"
        
        data = {
            "threshold": 0.5,
            "save_result_image": True
        }
        
        files = {"image": open(image_path, "rb")}
        
        try:
            response = self.session.post(url, data=data, files=files)
            print(f"POST /api/face-recognition/verify - Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Face verification completed!")
                print(f"Faces detected: {result.get('total_faces_detected')}")
                print(f"Matches found: {len(result.get('matches', []))}")
                
                for i, match in enumerate(result.get('matches', [])):
                    print(f"  Match {i+1}:")
                    print(f"    Name: {match.get('name')}")
                    print(f"    Confidence: {match.get('confidence'):.3f}")
                    print(f"    Distance: {match.get('distance'):.3f}")
                
                if result.get('image_path'):
                    print(f"Result image saved: {result.get('image_path')}")
                
                return result
            else:
                print(f"❌ Verification failed: {response.text}")
                return {}
                
        except Exception as e:
            print(f"❌ Error during verification: {e}")
            return {}
        finally:
            files["image"].close()
    
    def test_single_face_verification(self, image_path: str) -> Dict[str, Any]:
        """Test single face verification API"""
        print("🎯 Testing single face verification...")
        
        if not os.path.exists(image_path):
            print(f"❌ Image file not found: {image_path}")
            return {}
        
        url = f"{self.base_url}/api/face-recognition/verify-single"
        
        data = {"threshold": 0.5}
        files = {"image": open(image_path, "rb")}
        
        try:
            response = self.session.post(url, data=data, files=files)
            print(f"POST /api/face-recognition/verify-single - Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Single face verification completed!")
                
                if result.get('match_found'):
                    person = result.get('person', {})
                    print(f"Match found: {person.get('name')}")
                    print(f"Confidence: {result.get('confidence'):.3f}")
                    print(f"Distance: {result.get('distance'):.3f}")
                else:
                    print("No match found")
                
                return result
            else:
                print(f"❌ Verification failed: {response.text}")
                return {}
                
        except Exception as e:
            print(f"❌ Error during verification: {e}")
            return {}
        finally:
            files["image"].close()
    
    def test_get_settings(self) -> Dict[str, Any]:
        """Test get recognition settings"""
        print("⚙️ Testing get settings...")
        
        url = f"{self.base_url}/api/face-recognition/settings"
        
        response = self.session.get(url)
        print(f"GET /api/face-recognition/settings - Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Settings retrieved!")
            print(f"Default tolerance: {result.get('default_tolerance')}")
            print(f"Supported formats: {result.get('supported_formats')}")
            print(f"Max file size: {result.get('max_file_size_mb')}MB")
            return result
        else:
            print(f"❌ Failed to get settings: {response.text}")
            return {}
    
    def test_stats(self) -> Dict[str, Any]:
        """Test get stats"""
        print("📊 Testing stats...")
        
        url = f"{self.base_url}/api/persons/stats/summary"
        
        response = self.session.get(url)
        print(f"GET /api/persons/stats/summary - Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Stats retrieved!")
            print(f"Total persons: {result.get('total_persons')}")
            return result
        else:
            print(f"❌ Failed to get stats: {response.text}")
            return {}
    
    def run_all_tests(self, register_image: str = None, verify_image: str = None):
        """Run all tests"""
        print("🚀 Starting API tests...\n")
        
        # Test 1: Health check
        self.test_health_check()
        print("\n" + "="*50 + "\n")
        
        # Test 2: Get settings
        self.test_get_settings()
        print("\n" + "="*50 + "\n")
        
        # Test 3: Get initial stats
        self.test_stats()
        print("\n" + "="*50 + "\n")
        
        # Test 4: Get persons (should be empty initially)
        self.test_get_persons()
        print("\n" + "="*50 + "\n")
        
        # Test 5: Register person (if image provided)
        if register_image:
            person_result = self.test_register_person(register_image)
            print("\n" + "="*50 + "\n")
            
            if person_result:
                # Test 6: Get persons again (should have 1 person now)
                self.test_get_persons()
                print("\n" + "="*50 + "\n")
                
                # Test 7: Get stats again
                self.test_stats()
                print("\n" + "="*50 + "\n")
        
        # Test 8: Face verification (if image provided)
        if verify_image:
            self.test_face_verification(verify_image)
            print("\n" + "="*50 + "\n")
            
            self.test_single_face_verification(verify_image)
            print("\n" + "="*50 + "\n")
        
        print("✅ All tests completed!")


def main():
    """Main function to run tests"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test Face Recognition API')
    parser.add_argument('--base-url', default='http://localhost:8000', help='API base URL')
    parser.add_argument('--register-image', help='Image file for registration test')
    parser.add_argument('--verify-image', help='Image file for verification test')
    
    args = parser.parse_args()
    
    tester = FaceRecognitionAPITester(args.base_url)
    
    print(f"Testing API at: {args.base_url}")
    print(f"Register image: {args.register_image}")
    print(f"Verify image: {args.verify_image}")
    print("\n" + "="*50 + "\n")
    
    tester.run_all_tests(args.register_image, args.verify_image)


if __name__ == "__main__":
    main()