#!/usr/bin/env python3
"""
Script test nhanh để kiểm tra Face Recognition API
"""

import subprocess
import time
import requests
import sys
import os

def test_api_startup():
    """Test khởi động API"""
    print("🚀 Testing Face Recognition API startup...")
    
    # Start API in background
    print("📍 Starting API...")
    process = subprocess.Popen([
        sys.executable, "run.py"
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    # Wait for API to start
    print("⏳ Waiting for API to start...")
    time.sleep(5)
    
    try:
        # Test health endpoint
        response = requests.get("http://localhost:8000/health", timeout=10)
        if response.status_code == 200:
            print("✅ API Health Check: OK")
            print(f"📊 Response: {response.json()}")
        else:
            print(f"❌ API Health Check failed: {response.status_code}")
            return False
        
        # Test root endpoint
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            print("✅ Root endpoint: OK")
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
        
        # Test docs endpoint
        response = requests.get("http://localhost:8000/docs", timeout=5)
        if response.status_code == 200:
            print("✅ Swagger UI: OK")
        else:
            print(f"❌ Swagger UI failed: {response.status_code}")
        
        # Test settings endpoint
        response = requests.get("http://localhost:8000/api/face-recognition/settings", timeout=5)
        if response.status_code == 200:
            print("✅ Settings endpoint: OK")
            settings = response.json()
            print(f"📋 Default tolerance: {settings.get('default_tolerance')}")
            print(f"📁 Supported formats: {settings.get('supported_formats')}")
        else:
            print(f"❌ Settings endpoint failed: {response.status_code}")
        
        print("\n🎉 API is running successfully!")
        print("📍 API URL: http://localhost:8000")
        print("📚 Swagger UI: http://localhost:8000/docs")
        print("📖 ReDoc: http://localhost:8000/redoc")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error: {e}")
        return False
    
    finally:
        # Stop the API process
        print("\n🛑 Stopping API...")
        process.terminate()
        process.wait()

def main():
    """Main function"""
    print("=" * 60)
    print("Face Recognition API - Quick Test")
    print("=" * 60)
    
    # Check if in virtual environment
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️ Warning: Not in virtual environment")
        print("Please run: source venv/bin/activate")
    
    # Check if MongoDB is running
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 27017))
        sock.close()
        if result == 0:
            print("✅ MongoDB is running")
        else:
            print("❌ MongoDB is not running")
            print("Please start MongoDB first")
            return False
    except Exception as e:
        print(f"❌ Error checking MongoDB: {e}")
        return False
    
    # Test API
    success = test_api_startup()
    
    if success:
        print("\n✅ All tests passed!")
        print("\nYou can now run the API with:")
        print("  python run.py")
        return True
    else:
        print("\n❌ Some tests failed!")
        print("\nPlease check:")
        print("1. Virtual environment is activated")
        print("2. MongoDB is running")
        print("3. All packages are installed correctly")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)